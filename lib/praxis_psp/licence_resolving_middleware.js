'use strict'

const config = require('../config')
const ERROR_MESSAGES = require('../constants').ERROR_MESSAGES

const licenceToConf = {
  SEYCHELLES: config.PRAXIS.FSA,
  CYSEC: config.PRAXIS.CYSEC,
  FSA_OLD: config.PRAXIS.FSA_OLD  // TODO fallback default; remove after CX-2262 is live
}

module.exports = (req, res, next) => {
  req.query.licence = (req.query.licence || 'FSA_OLD').toUpperCase()  // TODO fallback default; remove after CX-2262 is live

  const conf = licenceToConf[req.query.licence]
  if (conf) {
    res.locals.praxisConf = conf
    next()
  } else {
    next(new Error(ERROR_MESSAGES.UNKNOWN_LICENCE))
  }
}
