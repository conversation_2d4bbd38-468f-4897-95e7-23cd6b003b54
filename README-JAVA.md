# Payment Service - Java Spring Boot Version

This is the Java Spring Boot 3.4.6 rewrite of the Node.js payment service, using Java 21, Gradle 8.14, and Liquibase for database migrations.

## Technology Stack

- **Java 21** - Latest LTS version with modern language features
- **Spring Boot 3.4.6** - Latest Spring Boot version with native compilation support
- **Gradle 8.14** - Build tool and dependency management
- **Liquibase** - Database migration and versioning
- **PostgreSQL** - Primary database
- **RabbitMQ** - Message broker for async processing
- **Spring Data JPA** - Data access layer
- **Spring Security** - Security framework
- **Spring AMQP** - RabbitMQ integration
- **WebFlux** - Reactive HTTP client for external service calls

## Project Structure

```
src/
├── main/
│   ├── java/com/skilling/payment/
│   │   ├── PaymentServiceApplication.java
│   │   ├── config/                 # Configuration classes
│   │   ├── controller/             # REST controllers
│   │   ├── domain/                 # JPA entities and enums
│   │   ├── repository/             # Spring Data repositories
│   │   ├── service/                # Business logic services
│   │   ├── messaging/              # RabbitMQ producers/consumers
│   │   └── external/               # External service clients
│   └── resources/
│       ├── application.yml         # Application configuration
│       └── db/changelog/           # Liquibase migrations
└── test/
    ├── java/                       # Unit and integration tests
    └── resources/
        └── application-test.yml    # Test configuration
```

## Key Features Implemented

### Core Functionality
- ✅ Transaction management (deposits, withdrawals, refunds)
- ✅ Account mapping between customer and payment accounts
- ✅ Payment method configuration
- ✅ Health check endpoints
- ✅ Database migrations with Liquibase

### External Integrations
- ✅ WebClient configuration for external services
- ✅ Customer service integration
- ✅ Trading balance service integration
- ✅ Exchange rate service integration
- ✅ Devcode/PaymentIQ integration (structure)
- ✅ Praxis PSP integration (structure)

### Messaging
- ✅ RabbitMQ configuration with exchanges and queues
- ✅ Dead letter queue setup
- ✅ Message producers and consumers structure

### Security & Monitoring
- ✅ CORS configuration
- ✅ JWT token validation setup
- ✅ Spring Boot Actuator for monitoring
- ✅ Structured logging configuration

## Getting Started

### Prerequisites
- Java 21
- Docker and Docker Compose
- PostgreSQL (or use Docker)
- RabbitMQ (or use Docker)

### Local Development

1. **Clone and build the project:**
   ```bash
   ./gradlew build
   ```

2. **Start dependencies with Docker:**
   ```bash
   docker-compose up -d payment-database rabbitmq
   ```

3. **Run database migrations:**
   ```bash
   ./gradlew liquibaseUpdate
   ```

4. **Start the application:**
   ```bash
   ./gradlew bootRun
   ```

The application will start on port 3000 and be available at http://localhost:3000

### Running Tests

```bash
# Run all tests
./gradlew test

# Run with coverage
./gradlew test jacocoTestReport
```

### Building Docker Image

```bash
# Build the application
./gradlew bootJar

# Build Docker image
docker build -f Dockerfile.java -t payment-service:latest .
```

## API Endpoints

### Health Check
- `GET /` - Service health check
- `GET /actuator/health` - Detailed health information

### Transactions
- `GET /payment/bo/transactions/firstDepositors` - Get first depositors
- `GET /payment/bo/transactions/{id}` - Get transaction by ID
- `GET /payment/bo/customer/{customerId}/transactions/{id}` - Get customer transaction
- `GET /payment/bo/transactions` - Search transactions
- `POST /payment/manual/customer/{customerId}/account/{accountId}/transaction` - Create manual transaction

### Payment Methods
- `GET /payment/methods` - Get available payment methods
- `GET /payment/methods/firstDeposit` - Get methods for first deposit

## Configuration

The application uses Spring Boot's configuration system with profiles:

- `application.yml` - Main configuration
- `application-local.yml` - Local development overrides
- `application-test.yml` - Test configuration

Key configuration properties:
- Database connection settings
- RabbitMQ connection settings
- External service URLs
- JWT signing keys
- PSP configuration (Devcode, Praxis)

## Database Migrations

Liquibase is used for database schema management:

```bash
# Apply migrations
./gradlew liquibaseUpdate

# Rollback last changeset
./gradlew liquibaseRollbackCount -PliquibaseCommandValue=1

# Generate diff
./gradlew liquibaseDiffChangeLog
```

## Monitoring and Observability

- **Health Checks**: `/actuator/health`
- **Metrics**: `/actuator/metrics`
- **Info**: `/actuator/info`
- **Prometheus**: `/actuator/prometheus`

## Migration Notes from Node.js

### Completed Conversions
1. **Express.js → Spring Boot Web**: REST controllers with proper validation
2. **Sequelize → Spring Data JPA**: Entity mapping with proper relationships
3. **Node.js migrations → Liquibase**: Database versioning and migration scripts
4. **amqplib → Spring AMQP**: RabbitMQ integration with proper error handling
5. **request-promise → WebClient**: Reactive HTTP client for external services
6. **Bunyan logging → Logback**: Structured logging with JSON output

### Architecture Improvements
1. **Type Safety**: Strong typing with Java vs dynamic typing in JavaScript
2. **Dependency Injection**: Spring's IoC container vs manual dependency management
3. **Transaction Management**: Declarative transactions with `@Transactional`
4. **Configuration Management**: Spring Boot's configuration properties
5. **Testing**: JUnit 5 with TestContainers for integration tests

### Still To Implement
1. Complete PSP integrations (Devcode, Praxis)
2. Message consumers for RabbitMQ
3. JWT token validation middleware
4. Location/IP geolocation service
5. Financial utilities and currency conversion
6. Comprehensive test suite
7. Docker Compose setup for Java version

## Contributing

1. Follow Java coding standards and Spring Boot best practices
2. Write tests for new functionality
3. Update documentation for API changes
4. Use conventional commits for version control

## License

This project is proprietary to Skilling.
