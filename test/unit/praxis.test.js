'use strict'

const expect = require('chai').expect
const nock = require('nock')
const httpStatusCodes = require('http-status-codes')
const praxisConf = require('../../lib/config').PRAXIS
const praxis = require('../../lib/external_services/praxis')
const appLog = require('../../lib/log')

const praxisLicenceConf = praxisConf.FSA
const okResponseFromPraxisCancelWd = {
  'status': 0,
  'description': 'Ok',
  'transaction': {
    'transaction_type': 'payout',
    'transaction_status': 'approved',
    'tid': 756859,
    'transaction_id': '13397',
    'currency': 'EUR',
    'amount': 100,
    'conversion_rate': 1.000000,
    'charge_currency': 'EUR',
    'charge_amount': 100,
    'fee': 0,
    'payment_method': 'Credit Card',
    'payment_processor': 'TestCardProcessor',
    'gateway': 's-pTSZyK23E1Ee5KZpcNbX_aFl0HuhQ0',
    'card': {
      'card_token': 'J-4-a0vPhjZ9R75JP98VDUFgbh9y8sYr',
      'card_type': 'VISA',
      'card_number': '411111******1111',
      'card_exp': '12/2024',
      'card_issuer_name': 'Bank of Somewhere',
      'card_issuer_country': 'GB'
    },
    'wallet': null,
    'is_3d': 0,
    'is_cascade': 0,
    'cascade_level': 0,
    'reference_id': null,
    'withdrawal_request_id': 756850,
    'account_identifier': null,
    'created_by': 'INTERNET',
    'edited_by': 'INTERNET',
    'status_code': 'SC-002',
    'status_details': 'Transaction approved'
  },
  'version': '1.3',
  'timestamp': **********
}

const okResponseFromPraxisFindTx = {
  'status': 0,
  'description': 'Ok',
  'customer': {
    'customer_token': '87cfb23a8f1e68e162c276b754d9c061',
    'country': 'GB',
    'first_name': 'John',
    'last_name': 'Johnson',
    'avs_alert': 0,
    'verification_alert': null
  },
  'session': {
    'auth_token': '8a7sd87a8sd778ac961062c6bedddb8',
    'intent': 'payment',
    'session_status': 'created',
    'order_id': 'test-**********',
    'currency': 'EUR',
    'amount': 100,
    'conversion_rate': 1.000000,
    'processed_currency': 'EUR',
    'processed_amount': 100,
    'payment_method': 'Credit Card',
    'gateway': null,
    'cid': '1',
    'variable1': 'your variable',
    'variable2': 'if that is not enough, you can pass even one more variable',
    'variable3': null
  },
  'transaction': {
    'transaction_type': 'sale',
    'transaction_status': 'approved',
    'tid': 756850,
    'transaction_id': '13397',
    'currency': 'EUR',
    'amount': 100,
    'conversion_rate': 1.000000,
    'charge_currency': 'EUR',
    'charge_amount': 100,
    'fee': 0,
    'payment_method': 'Credit Card',
    'payment_processor': 'TestCardProcessor',
    'gateway': 's-pTSZyK23E1Ee5KZpcNbX_aFl0HuhQ0',
    'card': {
      'card_token': 'J-4-a0vPhjZ9R75JP98VDUFgbh9y8sYr',
      'card_type': 'VISA',
      'card_number': '411111******1111',
      'card_exp': '12/2024',
      'card_issuer_name': 'Bank of Somewhere',
      'card_issuer_country': 'GB'
    },
    'wallet': null,
    'is_3d': 0,
    'is_cascade': 0,
    'cascade_level': 0,
    'reference_id': 756850,
    'withdrawal_request_id': 756853,
    'account_identifier': null,
    'created_by': 'INTERNET',
    'edited_by': 'INTERNET',
    'status_code': 'SC-002',
    'status_details': 'Transaction approved'
  },
  'version': '1.3',
  'timestamp': **********
}

describe('Praxis client', () => {
  describe('cancelWithdrawalRequest', () => {
    const wdReqId = '1236454'
    const opts = {log: appLog}

    it('should return ok response returned from praxis', () => {
      let actualRequestBodyToPraxis, signatureHeaderToPraxis

      nock(praxisConf.DOMAIN)
        .post('/agent/manage-withdrawal-request')
        .reply(function (uri, requestBody) {
          actualRequestBodyToPraxis = JSON.parse(requestBody)
          signatureHeaderToPraxis = this.req.headers[praxisConf.AUTH_HDR]
          return [
            httpStatusCodes.OK,
            okResponseFromPraxisCancelWd,
            { [praxisConf.AUTH_HDR]: '38c54e83d4c1709a9c021c1bd8ec04d178269e268f121031aadbab57add215922160a01fdba73dc67f160b3ff4185829' }
          ]
        })

      return praxis.cancelWithdrawalRequest(wdReqId, praxisLicenceConf, opts)
        .then(response => {
          expect(response).to.deep.equal(okResponseFromPraxisCancelWd)

          expect(actualRequestBodyToPraxis.merchant_id).to.equal('User-Skilling')
          expect(actualRequestBodyToPraxis.application_key).to.equal('Skilling TEST')
          expect(actualRequestBodyToPraxis.intent).to.equal('cancel-withdrawal-request')
          expect(actualRequestBodyToPraxis.withdrawal_request_id).to.equal(wdReqId)
          expect(actualRequestBodyToPraxis.version).to.equal('1.3')
          expect(actualRequestBodyToPraxis.timestamp).to.be.a('number')

          expect(signatureHeaderToPraxis).to.be.a('string')
        })
    })

    it('should throw error if response from praxis has incorrect signature', () => {
      nock(praxisConf.DOMAIN)
        .post('/agent/manage-withdrawal-request')
        .reply(
          httpStatusCodes.OK,
          okResponseFromPraxisCancelWd,
          { [praxisConf.AUTH_HDR]: 'non-matching-signature' }
        )

      return praxis.cancelWithdrawalRequest(wdReqId, praxisLicenceConf, opts)
        .catch(err => expect(err.message).to.equal('Invalid call'))
    })

    it('should throw error that resulted from the call to praxis', () => {
      nock(praxisConf.DOMAIN)
        .post('/agent/manage-withdrawal-request')
        .reply(httpStatusCodes.INTERNAL_SERVER_ERROR)

      return praxis.cancelWithdrawalRequest(wdReqId, praxisLicenceConf, opts)
        .catch(err => expect(err.message).to.equal('Error: Application error response received from Praxis: undefined. Description was [undefined]'))
    })
  })

  describe('findTransaction', () => {
    const txId = '12364999'
    const opts = {log: appLog}

    it('should return transaction', () => {
      let actualRequestBodyToPraxis, signatureHeaderToPraxis

      nock(praxisConf.DOMAIN)
        .post('/agent/find-transaction')
        .reply(function (uri, requestBody) {
          actualRequestBodyToPraxis = JSON.parse(requestBody)
          signatureHeaderToPraxis = this.req.headers[praxisConf.AUTH_HDR]
          return [
            httpStatusCodes.OK,
            okResponseFromPraxisFindTx,
            { [praxisConf.AUTH_HDR]: 'cfe506ba990d26cab891b03cdaa40f428b0b1e0579444ebeb8233b76c098d04c86b18f73e3273226a5bfa217f2f65c8c' }
          ]
        })

      return praxis.findTransaction(txId, praxisLicenceConf, opts)
        .then(response => {
          expect(response).to.deep.equal(okResponseFromPraxisFindTx.transaction)

          expect(actualRequestBodyToPraxis.merchant_id).to.equal('User-Skilling')
          expect(actualRequestBodyToPraxis.application_key).to.equal('Skilling TEST')
          expect(actualRequestBodyToPraxis.tid).to.equal(txId)
          expect(actualRequestBodyToPraxis.version).to.equal('1.3')
          expect(actualRequestBodyToPraxis.timestamp).to.be.a('number')

          expect(signatureHeaderToPraxis).to.be.a('string')
        })
    })

    it('should throw error if response from praxis has incorrect signature', () => {
      nock(praxisConf.DOMAIN)
        .post('/agent/find-transaction')
        .reply(
          httpStatusCodes.OK,
          okResponseFromPraxisFindTx,
          { [praxisConf.AUTH_HDR]: 'non-matching-signature' }
        )

      return praxis.findTransaction(txId, praxisLicenceConf, opts)
        .catch(err => expect(err.message).to.equal('Invalid call'))
    })

    it('should throw error that resulted from the call to praxis', () => {
      nock(praxisConf.DOMAIN)
        .post('/agent/find-transaction')
        .reply(httpStatusCodes.INTERNAL_SERVER_ERROR)

      return praxis.findTransaction(txId, praxisLicenceConf, opts)
        .catch(err => expect(err.message).to.equal('Error: Application error response received from Praxis: undefined. Description was [undefined]'))
    })
  })
})
