'use strict'

const expect = require('chai').expect
const request = require('supertest')
const httpStatusCodes = require('http-status-codes')
const sinon = require('sinon')
const moment = require('moment')
const nock = require('nock')
const models = require('../../../lib/db')
const app = require('../../../lib/app')
const config = require('../../../lib/config')
const rabbit = require('../../../lib/messaging')
const MERCHANT_ID = 'User-Skilling'
const MERCHANT_SECRET = 'Test'
const DO_NOTHING = () => {}

describe('Praxis notification', () => {
  let sandbox

  beforeEach(() => {
    sandbox = sinon.createSandbox()
  })

  afterEach(() => {
    sandbox.restore()
  })

  it('should handle deposit notification', () => {
    sandbox.stub(config.PRAXIS.FSA_OLD, 'MERCHANT_ID').value(MERCHANT_ID)
    sandbox.stub(config.PRAXIS.FSA_OLD, 'MERCHANT_SECRET').value(MERCHANT_SECRET)

    const customerId = '5277'
    const xTransactionId = '1e6f2e01-5dc0-4f32-8b9e-f67f94319844'
    const xSessionId = '1291-session-id'
    const externalId = '3000003'
    const tradingAccountId = '5ef18677-cea2-4be3-8441-c5836daca3f8'
    const traceId = 777944

    const notificationRequestBody = {
      merchant_id: 'User-Skilling',
      application_key: 'Skilling TEST',
      customer: {
        customer_token: '1234aaa'
      },
      session: {
        auth_token: '********************************',
        intent: 'payment',
        order_id: '',
        payment_method: 'VISA',
        cid: externalId,
        variable1: `${xTransactionId},${xSessionId},${customerId}`,
        variable2: null,
        variable3: null
      },
      transaction: {
        transaction_status: 'approved',
        transaction_type: 'sale',
        tid: traceId,
        transaction_id: '6312',
        currency: 'EUR',
        amount: 4000,
        processed_currency: 'EUR',
        processed_amount: 4000,
        created_by: 'INTERNET',
        edited_by: 'INTERNET',
        is_cascade: 0,
        cascade_level: 0,
        payment_method: 'VISA',
        payment_processor: 'TestPP',
        reference_id: null,

        card: {
          card_exp: '02/2021',
          card_number: '470810******0551',
          card_type: 'VISA'
        }
      },
      version: '1.3',
      timestamp: **********
    }

    sandbox.stub(models.AccountMapping, 'findOne').callsFake(() => Promise.resolve({
      customerId,
      externalId,
      tradingAccountId
    }))

    nock(config.EXCHANGE_RATE_SERVICE)
      .get('/latest')
      .query({
        base: 'EUR'
      })
      .reply(httpStatusCodes.OK, {
        rates: {
          'EUR': 1,
          'USD': 1.1245
        }
      })

    nock(config.CUSTOMER_SERVICE)
      .get(`/v2/customers/${customerId}`)
      .reply(httpStatusCodes.OK, {
        contactInformation: {country: 'BA'},
        personalInformation: {}
      })

    sandbox
      .stub(models.Transaction, 'count')
      .callsFake(() => Promise.resolve(0))

    sandbox
      .stub(models.Transaction, 'findOne')
      .callsFake(() => Promise.resolve(null))

    const transactionCreateStub = sandbox
      .stub(models.Transaction, 'create')
      .callsFake(() => Promise.resolve({
        customerId,
        id: '1',
        currency: 'EUR',
        currencySymbol: '€',
        cardLastFourDigits: '0551',
        amount: '40.00',
        reference: `p${traceId}`,
        externalId,
        psp: 'VISA',
        paymentProcessor: 'TestPP',
        createdAt: '2020-06-16 12:53:55.188+00',
        status: 'FINISHED',
        transactionType: 'DEPOSIT',
        destinationType: 'PRAXIS',
        isSeychelles: true,
        tradingAccountId
      }))

    const rabbitMqPublishStub = sandbox.stub(rabbit, 'publish').callsFake(DO_NOTHING)

    const responseTimeStamp = **********
    sandbox.stub(moment.fn, 'unix').callsFake(() => responseTimeStamp)

    const expectedResponse = {
      merchant_id: 'User-Skilling',
      timestamp: responseTimeStamp,
      version: '1.3',
      status: 0,
      description: 'Ok'
    }

    return request(app)
      .post(`/payment/praxis/notification`)
      .type('json')
      .set({
        'Content-Type': 'application/json',
        [config.PRAXIS.AUTH_HDR]: '80603b27868d8f55b2618018d39de0db2c120b0b1cc66f2fb6960673f0f9884a5891a7dd6a02dd8d5430d550c06377cf'
      })
      .send(notificationRequestBody)
      .expect('Content-Type', /json/)
      .expect(httpStatusCodes.OK)
      .then(response => {
        expect(transactionCreateStub.calledOnce).to.equal(true)

        expect(transactionCreateStub.firstCall.args[0].eurAmount).to.equal('40.00')
        expect(transactionCreateStub.firstCall.args[0].usdAmount).to.equal('44.98')
        expect(transactionCreateStub.firstCall.args[0].currencySymbol).to.equal('€')
        expect(transactionCreateStub.firstCall.args[0].cardLastFourDigits).to.equal('0551')
        expect(transactionCreateStub.firstCall.args[0].paymentProcessor).to.equal('TestPP')

        expect(rabbitMqPublishStub.calledOnce).to.equal(true)

        expect(rabbitMqPublishStub.firstCall.args[0]).to.deep.equal({
          type: 'DepositCompleted',
          customerId: '5277',
          routingKey: 'payment.deposit.completed',
          data: {
            transactionId: '1',
            currency: 'EUR',
            currencySymbol: '€',
            cardLastFourDigits: '0551',
            createdAt: '2020-06-16T12:53:55.188Z',
            amount: '40.00',
            reference: `p${traceId}`,
            destinationType: 'PRAXIS',
            psp: 'VISA',
            shortPspName: 'VISA',
            paymentProcessor: 'TestPP',
            transactionCreatedAt: '16/06/2020',
            status: 'FINISHED',
            type: 'DEPOSIT',
            license: 'SEYCHELLES',
            tradingAccountId,
            depositCount: 1
          },
          headers: {
            'x-transaction_id': xTransactionId,
            'x-session_id': xSessionId
          }
        })

        expect(response.body).to.deep.equal(expectedResponse)
      })
  })

  it('should handle deposit ERROR notification', () => {
    sandbox.stub(config.PRAXIS.FSA_OLD, 'MERCHANT_ID').value(MERCHANT_ID)
    sandbox.stub(config.PRAXIS.FSA_OLD, 'MERCHANT_SECRET').value(MERCHANT_SECRET)

    const customerId = '5278'
    const xTransactionId = '1e6f2e01-5dc0-4f32-8b9e-f67f94319844'
    const xSessionId = '1292-session-id'
    const externalId = '3000004'
    const tradingAccountId = '5ef18677-cea2-4be3-8441-c5836daca3f8'
    const traceId = 777945

    const notificationRequestBody = {
      merchant_id: 'User-Skilling',
      application_key: 'Skilling TEST',
      customer: {
        customer_token: '1234aaa'
      },
      session: {
        auth_token: '********************************',
        intent: 'payment',
        order_id: '',
        payment_method: 'VISA',
        cid: externalId,
        variable1: `${xTransactionId},${xSessionId},${customerId}`,
        variable2: null,
        variable3: null
      },
      transaction: {
        transaction_status: 'error',
        transaction_type: 'sale',
        tid: traceId,
        transaction_id: '6313',
        currency: 'EUR',
        amount: 4001,
        processed_currency: 'EUR',
        processed_amount: 4001,
        created_by: 'INTERNET',
        edited_by: 'INTERNET',
        is_cascade: 0,
        cascade_level: 0,
        payment_method: 'VISA',
        payment_processor: 'TestPP',
        reference_id: null,

        card: {
          card_exp: '02/2021',
          card_number: '470810******0551',
          card_type: 'VISA'
        }
      },
      version: '1.3',
      timestamp: **********
    }

    sandbox.stub(models.AccountMapping, 'findOne').callsFake(() => Promise.resolve({
      customerId,
      externalId,
      tradingAccountId
    }))

    nock(config.EXCHANGE_RATE_SERVICE)
      .get('/latest')
      .query({
        base: 'EUR'
      })
      .reply(httpStatusCodes.OK, {
        rates: {
          'EUR': 1,
          'USD': 1.1245
        }
      })

    nock(config.CUSTOMER_SERVICE)
      .get(`/v2/customers/${customerId}`)
      .reply(httpStatusCodes.OK, {
        contactInformation: {country: 'BA'},
        personalInformation: {}
      })

    sandbox
      .stub(models.Transaction, 'count')
      .callsFake(() => Promise.resolve(0))

    sandbox
      .stub(models.Transaction, 'findOne')
      .callsFake(() => Promise.resolve(null))

    const transactionCreateStub = sandbox
      .stub(models.Transaction, 'create')
      .callsFake(() => Promise.resolve({
        customerId,
        id: '1',
        currency: 'EUR',
        currencySymbol: '€',
        cardLastFourDigits: '0551',
        amount: '40.00',
        reference: `p${traceId}`,
        externalId,
        psp: 'VISA',
        paymentProcessor: 'TestPP',
        createdAt: '2020-06-16 12:53:55.188+00',
        status: 'FAILED',
        transactionType: 'DEPOSIT',
        destinationType: 'PRAXIS',
        isSeychelles: true,
        tradingAccountId
      }))

    const rabbitMqPublishStub = sandbox.stub(rabbit, 'publish').callsFake(DO_NOTHING)

    const responseTimeStamp = **********
    sandbox.stub(moment.fn, 'unix').callsFake(() => responseTimeStamp)

    const expectedResponse = {
      merchant_id: 'User-Skilling',
      timestamp: responseTimeStamp,
      version: '1.3',
      status: 0,
      description: 'Ok'
    }

    return request(app)
      .post(`/payment/praxis/notification`)
      .type('json')
      .set({
        'Content-Type': 'application/json',
        [config.PRAXIS.AUTH_HDR]: 'c3c73730dfe94e1fea72debf98992ce464ca8809465d6e9244076971bca93f6bd74fc391ed7db900c6ba1d566dc7f3aa'
      })
      .send(notificationRequestBody)
      .expect('Content-Type', /json/)
      .expect(httpStatusCodes.OK)
      .then(response => {
        expect(transactionCreateStub.calledOnce).to.equal(true)

        expect(transactionCreateStub.firstCall.args[0].eurAmount).to.equal('40.01')
        expect(transactionCreateStub.firstCall.args[0].usdAmount).to.equal('44.99')
        expect(transactionCreateStub.firstCall.args[0].currencySymbol).to.equal('€')
        expect(transactionCreateStub.firstCall.args[0].cardLastFourDigits).to.equal('0551')
        expect(transactionCreateStub.firstCall.args[0].paymentProcessor).to.equal('TestPP')

        expect(rabbitMqPublishStub.calledOnce).to.equal(true)

        expect(rabbitMqPublishStub.firstCall.args[0]).to.deep.equal({
          type: 'DepositFailed',
          customerId: '5278',
          routingKey: 'payment.deposit.failed',
          data: {
            transactionId: '1',
            currency: 'EUR',
            currencySymbol: '€',
            cardLastFourDigits: '0551',
            createdAt: '2020-06-16T12:53:55.188Z',
            amount: '40.00',
            reference: `p${traceId}`,
            destinationType: 'PRAXIS',
            psp: 'VISA',
            shortPspName: 'VISA',
            paymentProcessor: 'TestPP',
            transactionCreatedAt: '16/06/2020',
            status: 'FAILED',
            type: 'DEPOSIT',
            license: 'SEYCHELLES',
            tradingAccountId
          },
          headers: {
            'x-transaction_id': xTransactionId,
            'x-session_id': xSessionId
          }
        })

        expect(response.body).to.deep.equal(expectedResponse)
      })
  })

  it('should handle deposit ERROR notification 2', () => {
    sandbox.stub(config.PRAXIS.FSA_OLD, 'MERCHANT_ID').value(MERCHANT_ID)
    sandbox.stub(config.PRAXIS.FSA_OLD, 'MERCHANT_SECRET').value(MERCHANT_SECRET)

    const customerId = '5279'
    const xTransactionId = '1e6f2e01-5dc0-4f32-8b9e-f67f94319844'
    const xSessionId = '1292-session-id'
    const externalId = '3000005'
    const tradingAccountId = '5ef18677-cea2-4be3-8441-c5836daca3f8'
    const traceId = 777946

    const notificationRequestBody = {
      merchant_id: 'User-Skilling',
      application_key: 'Skilling TEST',
      customer: {
        customer_token: '1234aaa'
      },
      session: {
        auth_token: '********************************',
        intent: 'payment',
        order_id: '',
        payment_method: 'VISA',
        cid: externalId,
        variable1: `${xTransactionId},${xSessionId},${customerId}`,
        variable2: null,
        variable3: null
      },
      transaction: {
        transaction_status: 'error',
        transaction_type: 'sale',
        tid: traceId,
        transaction_id: '6313',
        currency: 'EUR',
        amount: 4002,
        processed_currency: 'EUR',
        processed_amount: 4002,
        created_by: 'INTERNET',
        edited_by: 'INTERNET',
        is_cascade: 0,
        cascade_level: 0,
        payment_method: 'VISA',
        payment_processor: 'TestPP',
        reference_id: null,

        card: {
          card_exp: '02/2021',
          card_number: '470810******0551',
          card_type: 'VISA'
        }
      },
      version: '1.3',
      timestamp: **********
    }

    sandbox.stub(models.AccountMapping, 'findOne').callsFake(() => Promise.resolve({
      customerId,
      externalId,
      tradingAccountId
    }))

    nock(config.EXCHANGE_RATE_SERVICE)
      .get('/latest')
      .query({
        base: 'EUR'
      })
      .reply(httpStatusCodes.OK, {
        rates: {
          'EUR': 1,
          'USD': 1.1245
        }
      })

    nock(config.CUSTOMER_SERVICE)
      .get(`/v2/customers/${customerId}`)
      .reply(httpStatusCodes.OK, {
        contactInformation: {country: 'BA'},
        personalInformation: {}
      })

    sandbox
      .stub(models.Transaction, 'count')
      .callsFake(() => Promise.resolve(0))

    sandbox
      .stub(models.Transaction, 'findOne')
      .callsFake(() => Promise.resolve(null))

    const transactionCreateStub = sandbox
      .stub(models.Transaction, 'create')
      .callsFake(() => Promise.resolve({
        customerId,
        id: '1',
        currency: 'EUR',
        currencySymbol: '€',
        cardLastFourDigits: '0551',
        amount: '40.00',
        reference: `p${traceId}`,
        externalId,
        psp: 'VISA',
        paymentProcessor: 'TestPP',
        createdAt: '2020-06-16 12:53:55.188+00',
        status: 'FAILED',
        transactionType: 'DEPOSIT',
        destinationType: 'PRAXIS',
        isSeychelles: true,
        tradingAccountId
      }))

    const rabbitMqPublishStub = sandbox.stub(rabbit, 'publish').callsFake(DO_NOTHING)

    const responseTimeStamp = **********
    sandbox.stub(moment.fn, 'unix').callsFake(() => responseTimeStamp)

    const expectedResponse = {
      merchant_id: 'User-Skilling',
      timestamp: responseTimeStamp,
      version: '1.3',
      status: 0,
      description: 'Ok'
    }

    return request(app)
      .post(`/payment/praxis/notification`)
      .type('json')
      .set({
        'Content-Type': 'application/json',
        [config.PRAXIS.AUTH_HDR]: 'e745669f8623f529edbb15434cd674373a6b8b9eaf1c855579b4f3977cf8f5d6794457381e881ffb1595801eead67ca6'
      })
      .send(notificationRequestBody)
      .expect('Content-Type', /json/)
      .expect(httpStatusCodes.OK)
      .then(response => {
        expect(transactionCreateStub.calledOnce).to.equal(true)

        expect(transactionCreateStub.firstCall.args[0].eurAmount).to.equal('40.02')
        expect(transactionCreateStub.firstCall.args[0].usdAmount).to.equal('45.00')
        expect(transactionCreateStub.firstCall.args[0].currencySymbol).to.equal('€')
        expect(transactionCreateStub.firstCall.args[0].cardLastFourDigits).to.equal('0551')
        expect(transactionCreateStub.firstCall.args[0].paymentProcessor).to.equal('TestPP')

        expect(rabbitMqPublishStub.calledOnce).to.equal(true)

        expect(rabbitMqPublishStub.firstCall.args[0]).to.deep.equal({
          type: 'DepositFailed',
          customerId: '5279',
          routingKey: 'payment.deposit.failed',
          data: {
            transactionId: '1',
            currency: 'EUR',
            currencySymbol: '€',
            cardLastFourDigits: '0551',
            createdAt: '2020-06-16T12:53:55.188Z',
            amount: '40.00',
            reference: `p${traceId}`,
            destinationType: 'PRAXIS',
            psp: 'VISA',
            shortPspName: 'VISA',
            paymentProcessor: 'TestPP',
            transactionCreatedAt: '16/06/2020',
            status: 'FAILED',
            type: 'DEPOSIT',
            license: 'SEYCHELLES',
            tradingAccountId
          },
          headers: {
            'x-transaction_id': xTransactionId,
            'x-session_id': xSessionId
          }
        })

        expect(response.body).to.deep.equal(expectedResponse)
      })
  })
})
