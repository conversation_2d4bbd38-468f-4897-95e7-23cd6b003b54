'use strict'

const chai = require('chai')
const chaiHttp = require('chai-http')
chai.use(chaiHttp)
const expect = chai.expect
const nock = require('nock')
const request = require('supertest')
const httpStatusCodes = require('http-status-codes')
const sinon = require('sinon')
const app = require('../../../lib/app')
const config = require('../../../lib/config')
const models = require('../../../lib/db')

describe('Init pay requests', () => {
  let sandbox

  const customerId = '17821'
  const externalId = '2'
  const paymentAccountId = '73WakrfVbNJBaAmhQtEeDv'
  const tradingAccountId = '75eb4e5b-ffc3-46ee-b871-2b27a16a120b'
  const ibId = '18dacd55-583b-46a1-9d38-23a9a72d350f'

  beforeEach(() => {
    sandbox = sinon.createSandbox()

    nock(config.CUSTOMER_SERVICE)
      .get(`/v2/customers/${customerId}`)
      .reply(httpStatusCodes.OK, {
        id: parseInt(customerId),
        registeredCurrency: 'EUR',
        personalInformation: {
          firstName: 'John',
          lastName: 'Doe',
          dateOfBirth: '1984-10-01'

        },
        contactInformation: {
          zipCode: '71000',
          city: 'Sarajevo',
          streetAddress: 'Whatever',
          country: 'BA',
          phoneCountryCode: '357',
          phoneNumber: '********',
          phoneNumberE164: '+***********'
        },
        email: '<EMAIL>'
      })

    nock(config.PARTNER_SERVICE)
      .get(`/v1/ibs/${ibId}/${customerId}`)
      .reply(httpStatusCodes.OK, {
        linked: false
      })

    sandbox.stub(models.AccountMapping, 'findOne').callsFake(() => Promise.resolve({
      customerId,
      paymentAccountId,
      externalId,
      tradingAccountId
    }))
  })

  afterEach(() => {
    sandbox.restore()
  })

  it('should throw an error in case of demo account usage', () => {
    nock(config.TAS_SERVICE)
      .get(`/v1/customers/${customerId}/accounts/${tradingAccountId}`)
      .reply(httpStatusCodes.OK, {
        demo: true,
        paymentsEnabled: true
      })

    return request(app)
      .post(`/payment/praxis/customer/${customerId}/account/${tradingAccountId}/deposit`)
      .type('json')
      .send({
        returnUrl: 'https://example.com',
        locale: 'en-GB'
      })
      .expect('Content-Type', /json/)
      .expect(httpStatusCodes.UNPROCESSABLE_ENTITY)
      .then(response => {
        expect(response.body.error).to.equal('Attempt to perform real funds operation on demo account')
      })
  })

  it('should throw an error on deposit init in case of a MAM/PAMM account', () => {
    nock(config.TAS_SERVICE)
      .get(`/v1/customers/${customerId}/accounts/${tradingAccountId}`)
      .reply(httpStatusCodes.OK, {
        paymentsEnabled: false
      })

    return request(app)
      .post(`/payment/praxis/customer/${customerId}/account/${tradingAccountId}/deposit`)
      .type('json')
      .send({
        returnUrl: 'https://example.com',
        locale: 'en-GB'
      })
      .expect('Content-Type', /json/)
      .expect(httpStatusCodes.UNPROCESSABLE_ENTITY)
      .then(response => {
        expect(response.body.error).to.equal('Attempt to perform payments operation on an account with paymentsEnabled=false')
      })
  })

  it('should throw an error on withdrawal init in case of a MAM/PAMM account', () => {
    nock(config.TAS_SERVICE)
      .get(`/v1/customers/${customerId}/accounts/${tradingAccountId}`)
      .reply(httpStatusCodes.OK, {
        paymentsEnabled: false
      })

    return request(app)
      .post(`/payment/praxis/customer/${customerId}/account/${tradingAccountId}/withdrawal`)
      .type('json')
      .send({
        returnUrl: 'https://example.com',
        locale: 'en-GB'
      })
      .expect('Content-Type', /json/)
      .expect(httpStatusCodes.UNPROCESSABLE_ENTITY)
      .then(response => {
        expect(response.body.error).to.equal('Attempt to perform payments operation on an account with paymentsEnabled=false')
      })
  })

  it('should handle funds operation with real account', () => {
    const requesterIp = '***************'

    nock(config.TAS_SERVICE)
      .get(`/v1/customers/${customerId}/accounts/${tradingAccountId}`)
      .reply(httpStatusCodes.OK, {
        demo: false,
        paymentsEnabled: true,
        currency: 'EEK'  // note it differs from customer.registeredCurrency
      })

    let actualRequestBodyToPraxis, signatureHeaderToPraxis

    nock(config.PRAXIS.CASHIER_URL)
      .post('')
      .reply(function (uri, requestBody) {
        actualRequestBodyToPraxis = requestBody
        signatureHeaderToPraxis = this.req.headers[config.PRAXIS.AUTH_HDR]
        const body = {
          status: 0,
          redirect_url: 'https://whatever.com/foo/bar/baz',
          timestamp: 1238876,
          customer: {
            customer_token: 'abs123'
          }
        }
        return [httpStatusCodes.OK, body]
      })

    return request(app)
      .post(`/payment/praxis/customer/${customerId}/account/${tradingAccountId}/deposit`)
      .set({
        'CF-Connecting-IP': requesterIp
      })
      .type('json')
      .send({
        returnUrl: 'https://example.com',
        locale: 'en-GB'
      })
      .expect('Content-Type', /json/)
      .expect(httpStatusCodes.OK)
      .then(response => {
        expect(response.body).to.deep.equal({ paymentUrl: 'https://whatever.com/foo/bar/baz' })

        actualRequestBodyToPraxis = JSON.parse(actualRequestBodyToPraxis)

        expect(actualRequestBodyToPraxis.merchant_id).to.equal('User-Skilling')
        expect(actualRequestBodyToPraxis.application_key).to.equal('Skilling TEST')
        expect(actualRequestBodyToPraxis.intent).to.equal('payment')
        expect(actualRequestBodyToPraxis.currency).to.equal('EEK')
        expect(actualRequestBodyToPraxis.cid).to.equal(externalId)
        expect(actualRequestBodyToPraxis.customer_data.first_name).to.equal('John')
        expect(actualRequestBodyToPraxis.customer_data.last_name).to.equal('Doe')
        expect(actualRequestBodyToPraxis.customer_data.dob).to.equal('10/01/1984')
        expect(actualRequestBodyToPraxis.customer_data.email).to.equal('<EMAIL>')
        expect(actualRequestBodyToPraxis.customer_data.address).to.equal('Whatever')
        expect(actualRequestBodyToPraxis.customer_data.city).to.equal('Sarajevo')
        expect(actualRequestBodyToPraxis.customer_data.country).to.equal('BA')
        expect(actualRequestBodyToPraxis.customer_data.zip).to.equal('71000')
        expect(actualRequestBodyToPraxis.customer_data.phone).to.equal('***********')
        expect(actualRequestBodyToPraxis.locale).to.equal('en-GB')
        expect(actualRequestBodyToPraxis.validation_url)
          .to.equal('https://devtest.skilling.com/g/payment/praxis/validation')

        expect(actualRequestBodyToPraxis.notification_url)
          .to.equal('https://devtest.skilling.com/g/payment/praxis/notification')
        expect(actualRequestBodyToPraxis.return_url)
          .to.equal('https://example.com')
        expect(actualRequestBodyToPraxis.timestamp).to.be.a('number')
        expect(actualRequestBodyToPraxis.version).to.equal('1.3')
        expect(actualRequestBodyToPraxis.variable1).to.match(new RegExp(`^[-0-9a-z]+,,${customerId}$`))
        expect(actualRequestBodyToPraxis.variable2).to.equal(undefined)
        expect(actualRequestBodyToPraxis.variable3).to.equal(undefined)
        expect(actualRequestBodyToPraxis.requester_ip).to.equal(requesterIp)
        expect(actualRequestBodyToPraxis.order_id).to.be.a('string')

        expect(signatureHeaderToPraxis).to.be.a('string')
      })
  })

  it('should attach variable2:skilling_global to request if user belongs to select GLOBAL licence customer group', () => {
    // given
    const customerId = '29453'

    nock(config.CUSTOMER_SERVICE)
      .get(`/v2/customers/${customerId}`)
      .reply(httpStatusCodes.OK, {
        id: parseInt(customerId),
        registeredCurrency: 'EUR',
        personalInformation: {
          firstName: 'John',
          lastName: 'Doe',
          dateOfBirth: '1984-10-01'
        },
        contactInformation: {
          zipCode: '71000',
          city: 'Sarajevo',
          streetAddress: 'Whatever',
          country: 'BA',
          phoneCountryCode: '357',
          phoneNumber: '********'
        },
        email: '<EMAIL>',
        isSkillingGlobal: true
      })

    sandbox.restore()
    sandbox.stub(models.AccountMapping, 'findOne').callsFake(() => Promise.resolve({
      customerId,
      paymentAccountId,
      externalId,
      tradingAccountId
    }))

    nock(config.TAS_SERVICE)
      .get(`/v1/customers/${customerId}/accounts/${tradingAccountId}`)
      .reply(httpStatusCodes.OK, {
        demo: false,
        paymentsEnabled: true
      })

    let actualRequestBodyToPraxis

    nock(config.PRAXIS.CASHIER_URL)
      .filteringRequestBody(requestBody => {
        actualRequestBodyToPraxis = requestBody
      })
      .post('')
      .reply(httpStatusCodes.OK, {
        status: 0,
        redirect_url: 'https://whatever.com/foo/bar/baz',
        timestamp: 1238876,
        customer: {
          customer_token: 'abs123'
        }
      })

    // expect
    return request(app)
      .post(`/payment/praxis/customer/${customerId}/account/${tradingAccountId}/deposit`)
      .type('json')
      .send({
        returnUrl: 'https://example.com',
        locale: 'en-GB'
      })
      .expect('Content-Type', /json/)
      .expect(httpStatusCodes.OK)
      .then(response => {
        actualRequestBodyToPraxis = JSON.parse(actualRequestBodyToPraxis)

        expect(actualRequestBodyToPraxis.variable1).to.match(new RegExp(`^[-0-9a-z]+,,${customerId}$`))
        expect(actualRequestBodyToPraxis.variable2).to.equal('skilling_global')
        expect(actualRequestBodyToPraxis.variable3).to.equal(undefined)
      })
  })
})
