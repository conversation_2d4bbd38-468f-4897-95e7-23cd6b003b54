'use strict'

const chai = require('chai')
const chaiHttp = require('chai-http')
chai.use(chaiHttp)
const expect = chai.expect
const moment = require('moment')
const nock = require('nock')
const sinon = require('sinon')
const request = require('supertest')
const httpStatusCodes = require('http-status-codes')
const models = require('../../../lib/db')
const app = require('../../../lib/app')
const config = require('../../../lib/config')
const MERCHANT_ID = 'Test'
const MERCHANT_SECRET = 'Test'

describe('Customer Sync Url', () => {
  let sandbox

  beforeEach(() => {
    sandbox = sinon.createSandbox()
  })

  afterEach(() => {
    sandbox.restore()
  })

  it('should respond with success if all checks are passed', () => {
    const customerId = 1
    const externalId = '1234'
    const tradingAccountId = '1752f6b1-2e50-4a6e-aaa3-7baa72d98bd7'
    const responseTimeStamp = **********

    sandbox.stub(config.PRAXIS.FSA_OLD, 'MERCHANT_ID').value(MERCHANT_ID)
    sandbox.stub(config.PRAXIS.FSA_OLD, 'MERCHANT_SECRET').value(MERCHANT_SECRET)
    sandbox.stub(moment.fn, 'unix').callsFake(() => responseTimeStamp)

    nock(config.CUSTOMER_SERVICE)
      .get(`/v2/customers/${customerId}`)
      .reply(httpStatusCodes.OK, {
        id: customerId,
        registeredCurrency: 'EUR',
        personalInformation: {
          firstName: 'John',
          lastName: 'Doe'
        },
        contactInformation: {
          zipCode: '12345',
          country: 'BA'
        },
        email: '<EMAIL>'
      })

    sandbox.stub(models.AccountMapping, 'findOne').callsFake(() => Promise.resolve({
      customerId,
      externalId,
      tradingAccountId
    }))

    nock(config.TRADING_BALANCE_SERVICE)
      .get(`/v1/customers/${customerId}/accounts/${tradingAccountId}/balance`)
      .reply(httpStatusCodes.OK, {
        withdrawableAmount: 300.00
      })

    return request(app)
      .post(`/payment/praxis/sync`)
      .type('json')
      .set({
        'Content-Type': 'application/json',
        /**
         * Match above structure with MerchantId = 'Test'
         */
        'GT-Authentication': '516289cbbbc5d11e151bdb5bda73c7020b3a177a08467196787c6d18b4a6fce482e8938da974ecaa3d5b026ba195821e'
      })
      .send({
        merchant_id: MERCHANT_ID,
        application_key: 'Praxis TEST',
        cid: externalId,
        timestamp: **********,
        version: '1.3'
      })
      .expect('Content-Type', /json/)
      .expect(httpStatusCodes.OK)
      .then(response => {
        const EXPECTED_SIGNATURE = '7a12cfd6fba2e59bf48a9aece789aea66de9397baecda9c35601018dff71582671db6f8347292292c85bfb282a3af100'

        expect(response.body).to.deep.equal({
          status: 0,
          description: 'Ok',
          manual_validation_url: 'https://devtest.skilling.com/g/payment/praxis/validation',
          manual_notification_url: 'https://devtest.skilling.com/g/payment/praxis/notification',
          version: '1.3',
          timestamp: responseTimeStamp,
          balance: 30000,
          customer_data: {
            country: 'BA',
            first_name: 'John',
            last_name: 'Doe',
            email: '<EMAIL>',
            zip: '12345'
          }
        })

        expect(response).to.have.header(config.PRAXIS.AUTH_HDR, EXPECTED_SIGNATURE)
      })
  })
})
