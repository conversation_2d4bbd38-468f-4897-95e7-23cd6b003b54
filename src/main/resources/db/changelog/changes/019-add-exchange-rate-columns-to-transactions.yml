databaseChangeLog:
  - changeSet:
      id: 019-add-exchange-rate-columns-to-transactions
      author: payment-service
      changes:
        - addColumn:
            tableName: transactions
            columns:
              - column:
                  name: exchanged_to
                  type: VARCHAR(255)
                  constraints:
                    nullable: true
              - column:
                  name: exchange_rate
                  type: DECIMAL(12,2)
                  constraints:
                    nullable: true
              - column:
                  name: exchanged_amount
                  type: DECIMAL(12,2)
                  constraints:
                    nullable: true
              - column:
                  name: exchange_markup
                  type: DECIMAL(12,2)
                  constraints:
                    nullable: true
      rollback:
        - dropColumn:
            tableName: transactions
            columnName: exchanged_to
        - dropColumn:
            tableName: transactions
            columnName: exchange_rate
        - dropColumn:
            tableName: transactions
            columnName: exchanged_amount
        - dropColumn:
            tableName: transactions
            columnName: exchange_markup
