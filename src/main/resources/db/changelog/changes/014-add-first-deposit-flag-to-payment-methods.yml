databaseChangeLog:
  - changeSet:
      id: 014-add-first-deposit-flag-to-payment-methods
      author: payment-service
      changes:
        - sql:
            sql: |
              UPDATE "payment_methods"
              SET "available_for_first_deposit" = true
      rollback:
        - sql:
            sql: |
              UPDATE "payment_methods"
              SET "available_for_first_deposit" = false
              WHERE brand = 'Neteller' OR brand = 'Skrill'
