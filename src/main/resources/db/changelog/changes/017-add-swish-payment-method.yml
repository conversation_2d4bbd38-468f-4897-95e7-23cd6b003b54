databaseChangeLog:
  - changeSet:
      id: 017-add-swish-payment-method
      author: payment-service
      changes:
        - insert:
            tableName: payment_methods
            columns:
              - column:
                  name: type
                  value: "Swish"
              - column:
                  name: brand
                  value: "Swish"
              - column:
                  name: available_for_first_deposit
                  valueBoolean: true
              - column:
                  name: enabled
                  valueBoolean: true
              - column:
                  name: primary
                  valueBoolean: false
              - column:
                  name: created_at
                  valueComputed: "CURRENT_TIMESTAMP"
              - column:
                  name: updated_at
                  valueComputed: "CURRENT_TIMESTAMP"
      rollback:
        - sql:
            sql: |
              DELETE FROM payment_methods
              WHERE type = 'Swish'
