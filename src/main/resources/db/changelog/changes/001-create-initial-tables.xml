<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
        http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.20.xsd">

    <changeSet id="001-create-verifications-table" author="payment-service">
        <createTable tableName="verifications">
            <column name="id" type="BIGSERIAL" autoIncrement="true">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="session_id" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="customer_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="payment_account_id" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="incoming_request" type="JSONB"/>
            <column name="destination_response" type="JSONB"/>
            <column name="created_at" type="TIMESTAMP" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="updated_at" type="TIMESTAMP" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>

    <changeSet id="001-create-authorizations-table" author="payment-service">
        <createTable tableName="authorizations">
            <column name="id" type="BIGSERIAL" autoIncrement="true">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="session_id" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="customer_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="payment_account_id" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="amount" type="DECIMAL(12,2)">
                <constraints nullable="false"/>
            </column>
            <column name="currency" type="VARCHAR(3)">
                <constraints nullable="false"/>
            </column>
            <column name="incoming_request" type="JSONB"/>
            <column name="destination_response" type="JSONB"/>
            <column name="created_at" type="TIMESTAMP" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="updated_at" type="TIMESTAMP" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>

    <changeSet id="001-create-transactions-table" author="payment-service">
        <createTable tableName="transactions">
            <column name="id" type="BIGSERIAL" autoIncrement="true">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="customer_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="amount" type="DECIMAL(12,2)">
                <constraints nullable="false"/>
            </column>
            <column name="currency" type="VARCHAR(3)">
                <constraints nullable="false"/>
            </column>
            <column name="status" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <column name="transaction_type" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <column name="external_id" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="destination_type" type="VARCHAR(50)" defaultValue="XSTATION"/>
            <column name="destination_status" type="VARCHAR(50)"/>
            <column name="psp" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="incoming_request" type="JSONB"/>
            <column name="destination_response" type="JSONB" defaultValue="{}"/>
            <column name="reference" type="VARCHAR(255)"/>
            <column name="comment" type="VARCHAR(255)"/>
            <column name="created_at" type="TIMESTAMP" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="updated_at" type="TIMESTAMP" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>

    <changeSet id="001-create-cancellations-table" author="payment-service">
        <createTable tableName="cancellations">
            <column name="id" type="BIGSERIAL" autoIncrement="true">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="session_id" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="customer_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="payment_account_id" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="incoming_request" type="JSONB"/>
            <column name="destination_response" type="JSONB"/>
            <column name="created_at" type="TIMESTAMP" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="updated_at" type="TIMESTAMP" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>

</databaseChangeLog>
