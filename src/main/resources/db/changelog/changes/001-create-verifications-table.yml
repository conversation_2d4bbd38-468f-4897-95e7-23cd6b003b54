databaseChangeLog:
  - changeSet:
      id: 001-create-verifications-table
      author: payment-service
      changes:
        - createTable:
            tableName: verifications
            columns:
              - column:
                  name: id
                  type: BIGSERIAL
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: session_id
                  type: VARCHAR(255)
                  constraints:
                    nullable: false
              - column:
                  name: customer_id
                  type: BIGINT
                  constraints:
                    nullable: false
              - column:
                  name: payment_account_id
                  type: VARCHAR(255)
                  constraints:
                    nullable: false
              - column:
                  name: incoming_request
                  type: JSONB
              - column:
                  name: destination_response
                  type: JSONB
              - column:
                  name: created_at
                  type: TIMESTAMP
                  defaultValueComputed: CURRENT_TIMESTAMP
                  constraints:
                    nullable: false
              - column:
                  name: updated_at
                  type: TIMESTAMP
                  defaultValueComputed: CURRENT_TIMESTAMP
                  constraints:
                    nullable: false
