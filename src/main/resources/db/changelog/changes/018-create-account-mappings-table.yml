databaseChangeLog:
  - changeSet:
      id: 018-create-account-mappings-table
      author: payment-service
      changes:
        - createTable:
            tableName: account_mappings
            columns:
              - column:
                  name: id
                  type: BIGSERIAL
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: customer_id
                  type: BIGINT
                  constraints:
                    nullable: false
              - column:
                  name: c_broker_account_id
                  type: BIGINT
                  constraints:
                    nullable: false
              - column:
                  name: payment_account_id
                  type: BIGINT
                  constraints:
                    nullable: false
              - column:
                  name: created_at
                  type: TIMESTAMP
                  defaultValueComputed: CURRENT_TIMESTAMP
                  constraints:
                    nullable: false
              - column:
                  name: updated_at
                  type: TIMESTAMP
                  defaultValueComputed: CURRENT_TIMESTAMP
                  constraints:
                    nullable: false
        - createIndex:
            tableName: account_mappings
            indexName: UX_account_mappings_c_broker_account_id
            unique: true
            columns:
              - column:
                  name: c_broker_account_id
        - createIndex:
            tableName: account_mappings
            indexName: UX_account_mappings_payment_account_id
            unique: true
            columns:
              - column:
                  name: payment_account_id
      rollback:
        - dropTable:
            tableName: account_mappings
