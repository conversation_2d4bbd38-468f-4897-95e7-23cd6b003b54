databaseChangeLog:
  - changeSet:
      id: 010-create-payment-methods-table
      author: payment-service
      changes:
        - createTable:
            tableName: payment_methods
            columns:
              - column:
                  name: id
                  type: BIGSERIAL
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: name
                  type: VARCHAR(255)
                  constraints:
                    nullable: false
              - column:
                  name: available_for_first_deposit
                  type: BOOLEAN
                  constraints:
                    nullable: false
              - column:
                  name: enabled
                  type: BOOLEAN
                  constraints:
                    nullable: false
              - column:
                  name: created_at
                  type: TIMESTAMP
                  defaultValueComputed: CURRENT_TIMESTAMP
                  constraints:
                    nullable: false
              - column:
                  name: updated_at
                  type: TIMESTAMP
                  defaultValueComputed: CURRENT_TIMESTAMP
                  constraints:
                    nullable: false
        - insert:
            tableName: payment_methods
            columns:
              - column:
                  name: name
                  value: "CreditCards"
              - column:
                  name: available_for_first_deposit
                  valueBoolean: true
              - column:
                  name: enabled
                  valueBoolean: true
              - column:
                  name: created_at
                  valueComputed: "CURRENT_TIMESTAMP"
              - column:
                  name: updated_at
                  valueComputed: "CURRENT_TIMESTAMP"
        - insert:
            tableName: payment_methods
            columns:
              - column:
                  name: name
                  value: "Trustly"
              - column:
                  name: available_for_first_deposit
                  valueBoolean: true
              - column:
                  name: enabled
                  valueBoolean: true
              - column:
                  name: created_at
                  valueComputed: "CURRENT_TIMESTAMP"
              - column:
                  name: updated_at
                  valueComputed: "CURRENT_TIMESTAMP"
        - insert:
            tableName: payment_methods
            columns:
              - column:
                  name: name
                  value: "BankTransfer"
              - column:
                  name: available_for_first_deposit
                  valueBoolean: true
              - column:
                  name: enabled
                  valueBoolean: true
              - column:
                  name: created_at
                  valueComputed: "CURRENT_TIMESTAMP"
              - column:
                  name: updated_at
                  valueComputed: "CURRENT_TIMESTAMP"
        - insert:
            tableName: payment_methods
            columns:
              - column:
                  name: name
                  value: "Skrill"
              - column:
                  name: available_for_first_deposit
                  valueBoolean: false
              - column:
                  name: enabled
                  valueBoolean: true
              - column:
                  name: created_at
                  valueComputed: "CURRENT_TIMESTAMP"
              - column:
                  name: updated_at
                  valueComputed: "CURRENT_TIMESTAMP"
        - insert:
            tableName: payment_methods
            columns:
              - column:
                  name: name
                  value: "Neteller"
              - column:
                  name: available_for_first_deposit
                  valueBoolean: false
              - column:
                  name: enabled
                  valueBoolean: true
              - column:
                  name: created_at
                  valueComputed: "CURRENT_TIMESTAMP"
              - column:
                  name: updated_at
                  valueComputed: "CURRENT_TIMESTAMP"
