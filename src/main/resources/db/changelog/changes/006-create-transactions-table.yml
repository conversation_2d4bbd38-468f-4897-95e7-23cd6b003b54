databaseChangeLog:
  - changeSet:
      id: 006-create-transactions-table
      author: payment-service
      changes:
        - createTable:
            tableName: transactions
            columns:
              - column:
                  name: id
                  type: BIGSERIAL
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: customer_id
                  type: BIGINT
                  constraints:
                    nullable: false
              - column:
                  name: amount
                  type: DECIMAL(12,2)
                  constraints:
                    nullable: false
              - column:
                  name: currency
                  type: VARCHAR(3)
                  constraints:
                    nullable: false
              - column:
                  name: status
                  type: VARCHAR(50)
                  constraints:
                    nullable: false
              - column:
                  name: transaction_type
                  type: VARCHAR(50)
                  constraints:
                    nullable: false
              - column:
                  name: destination_id
                  type: BIGINT
                  constraints:
                    nullable: false
              - column:
                  name: destination_type
                  type: VARCHAR(50)
                  defaultValue: "XSTATION"
              - column:
                  name: psp
                  type: VARCHAR(255)
                  constraints:
                    nullable: false
                  defaultValue: "DEVCODE"
              - column:
                  name: incoming_request
                  type: JSONB
              - column:
                  name: destination_response
                  type: JSONB
                  defaultValue: "{}"
              - column:
                  name: reference
                  type: <PERSON><PERSON><PERSON><PERSON>(255)
              - column:
                  name: created_at
                  type: TIMESTAMP
                  defaultValueComputed: CURRENT_TIMESTAMP
                  constraints:
                    nullable: false
              - column:
                  name: updated_at
                  type: TIMESTAMP
                  defaultValueComputed: CURRENT_TIMES<PERSON><PERSON>
                  constraints:
                    nullable: false
