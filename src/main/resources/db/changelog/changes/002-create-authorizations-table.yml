databaseChangeLog:
  - changeSet:
      id: 002-create-authorizations-table
      author: payment-service
      changes:
        - createTable:
            tableName: authorizations
            columns:
              - column:
                  name: id
                  type: BIGSERIAL
                  autoIncrement: true
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: session_id
                  type: VARCHAR(255)
                  constraints:
                    nullable: false
              - column:
                  name: customer_id
                  type: BIGINT
                  constraints:
                    nullable: false
              - column:
                  name: payment_account_id
                  type: VARCHAR(255)
                  constraints:
                    nullable: false
              - column:
                  name: amount
                  type: DECIMAL(12,2)
                  constraints:
                    nullable: false
              - column:
                  name: currency
                  type: VARCHAR(3)
                  constraints:
                    nullable: false
              - column:
                  name: incoming_request
                  type: JSONB
              - column:
                  name: destination_response
                  type: JSONB
              - column:
                  name: created_at
                  type: TIMESTAMP
                  defaultValueComputed: CURRENT_TIMESTAMP
                  constraints:
                    nullable: false
              - column:
                  name: updated_at
                  type: TIMESTAMP
                  defaultValueComputed: CURRENT_TIMESTAMP
                  constraints:
                    nullable: false
