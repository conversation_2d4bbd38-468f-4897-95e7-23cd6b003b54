databaseChangeLog:
  - include:
      file: db/changelog/changes/001-create-verifications-table.yml
  - include:
      file: db/changelog/changes/002-create-authorizations-table.yml
  - include:
      file: db/changelog/changes/003-create-transfers-table.yml
  - include:
      file: db/changelog/changes/004-create-cancellations-table.yml
  - include:
      file: db/changelog/changes/005-add-destination-columns-to-transfers.yml
  - include:
      file: db/changelog/changes/006-create-transactions-table.yml
  - include:
      file: db/changelog/changes/007-change-transaction-columns.yml
  - include:
      file: db/changelog/changes/008-add-destination-response-to-verifications.yml
  - include:
      file: db/changelog/changes/009-add-destination-status-to-transactions.yml
  - include:
      file: db/changelog/changes/010-create-payment-methods-table.yml
  - include:
      file: db/changelog/changes/011-delete-bank-transfer-payment-method.yml
  - include:
      file: db/changelog/changes/012-delete-transfers-table.yml
  - include:
      file: db/changelog/changes/013-add-type-column-to-payment-methods.yml
  - include:
      file: db/changelog/changes/014-add-first-deposit-flag-to-payment-methods.yml
  - include:
      file: db/changelog/changes/015-add-comment-column-to-transactions.yml
  - include:
      file: db/changelog/changes/016-add-sofort-payment-method.yml
  - include:
      file: db/changelog/changes/017-add-swish-payment-method.yml
  - include:
      file: db/changelog/changes/018-create-account-mappings-table.yml
  - include:
      file: db/changelog/changes/019-add-exchange-rate-columns-to-transactions.yml
  - include:
      file: db/changelog/changes/020-change-payment-account-id-column-type.yml
  - include:
      file: db/changelog/changes/021-change-user-id-column-type.yml
  - include:
      file: db/changelog/changes/022-change-destination-id-column-type.yml
  - include:
      file: db/changelog/changes/023-add-seychelles-column-to-transactions.yml
  - include:
      file: db/changelog/changes/024-add-payment-processor-columns-to-transactions.yml
  - include:
      file: db/changelog/changes/025-add-trading-account-id-to-transactions.yml
  - include:
      file: db/changelog/changes/026-add-trading-account-id-to-account-mappings.yml
  - include:
      file: db/changelog/changes/027-allow-null-external-id-in-account-mappings.yml
  - include:
      file: db/changelog/changes/028-rename-destination-id-to-external-id.yml
  - include:
      file: db/changelog/changes/029-rename-external-id-in-account-mappings.yml
  - include:
      file: db/changelog/changes/030-add-eur-usd-amounts-to-transactions.yml
  - include:
      file: db/changelog/changes/031-add-manual-bank-payment-methods.yml
  - include:
      file: db/changelog/changes/032-add-manual-bank-info-to-transactions.yml
  - include:
      file: db/changelog/changes/033-add-currency-card-columns-to-transactions.yml
  - include:
      file: db/changelog/changes/034-increase-session-id-length-in-verifications.yml
  - include:
      file: db/changelog/changes/035-add-paypal-payment-method.yml
  - include:
      file: db/changelog/changes/036-remove-paypal-payment-method.yml
  - include:
      file: db/changelog/changes/037-re-add-paypal-payment-method.yml
  - include:
      file: db/changelog/changes/038-change-session-id-type-in-verifications.yml
  - include:
      file: db/changelog/changes/039-increase-exchanged-amount-size.yml
