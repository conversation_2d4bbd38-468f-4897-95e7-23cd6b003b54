package com.skilling.payment.domain;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.skilling.payment.domain.enums.DestinationStatus;
import com.skilling.payment.domain.enums.DestinationType;
import com.skilling.payment.domain.enums.TransactionStatus;
import com.skilling.payment.domain.enums.TransactionType;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Map;

@Entity
@Table(name = "transactions")
public class Transaction {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @NotNull
    @Column(name = "customer_id", nullable = false)
    private Long customerId;

    @NotNull
    @Positive
    @Column(nullable = false, precision = 12, scale = 2)
    private BigDecimal amount;

    @NotBlank
    @Column(nullable = false, length = 3)
    private String currency;

    @Column(name = "currency_symbol")
    private String currencySymbol;

    @Column(name = "card_last_four_digits", length = 4)
    private String cardLastFourDigits;

    @NotNull
    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private TransactionStatus status;

    @NotNull
    @Enumerated(EnumType.STRING)
    @Column(name = "transaction_type", nullable = false)
    private TransactionType transactionType;

    @NotBlank
    @Column(name = "external_id", nullable = false)
    private String externalId;

    @Enumerated(EnumType.STRING)
    @Column(name = "destination_type")
    private DestinationType destinationType = DestinationType.XSTATION;

    @Enumerated(EnumType.STRING)
    @Column(name = "destination_status")
    private DestinationStatus destinationStatus;

    @NotBlank
    @Column(nullable = false)
    private String psp;

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "incoming_request", columnDefinition = "jsonb")
    private Map<String, Object> incomingRequest;

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "destination_response", columnDefinition = "jsonb")
    private Map<String, Object> destinationResponse;

    private String reference;

    private String comment;

    @Column(name = "exchanged_to")
    private String exchangedTo;

    @Column(name = "exchange_rate", precision = 12, scale = 2)
    private BigDecimal exchangeRate;

    @Column(name = "exchanged_amount", precision = 17, scale = 2)
    private BigDecimal exchangedAmount;

    @Column(name = "exchange_markup", precision = 12, scale = 2)
    private BigDecimal exchangeMarkup;

    @Column(name = "is_seychelles")
    private Boolean isSeychelles = false;

    @Column(name = "payment_method")
    private String paymentMethod;

    @Column(name = "payment_processor")
    private String paymentProcessor;

    @Column(name = "trading_account_id")
    private String tradingAccountId;

    @Column(name = "eur_amount", precision = 12, scale = 2)
    private BigDecimal eurAmount;

    @Column(name = "usd_amount", precision = 12, scale = 2)
    private BigDecimal usdAmount;

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "manual_bank_info", columnDefinition = "jsonb")
    private Map<String, Object> manualBankInfo;

    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;

    @Column(name = "updated_at")
    private LocalDateTime updatedAt;

    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
        updatedAt = LocalDateTime.now();
    }

    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }

    // Constructors
    public Transaction() {}

    // Getters and Setters
    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }

    public Long getCustomerId() { return customerId; }
    public void setCustomerId(Long customerId) { this.customerId = customerId; }

    public BigDecimal getAmount() { return amount; }
    public void setAmount(BigDecimal amount) { this.amount = amount; }

    public String getCurrency() { return currency; }
    public void setCurrency(String currency) { this.currency = currency; }

    public String getCurrencySymbol() { return currencySymbol; }
    public void setCurrencySymbol(String currencySymbol) { this.currencySymbol = currencySymbol; }

    public String getCardLastFourDigits() { return cardLastFourDigits; }
    public void setCardLastFourDigits(String cardLastFourDigits) { this.cardLastFourDigits = cardLastFourDigits; }

    public TransactionStatus getStatus() { return status; }
    public void setStatus(TransactionStatus status) { this.status = status; }

    public TransactionType getTransactionType() { return transactionType; }
    public void setTransactionType(TransactionType transactionType) { this.transactionType = transactionType; }

    public String getExternalId() { return externalId; }
    public void setExternalId(String externalId) { this.externalId = externalId; }

    public DestinationType getDestinationType() { return destinationType; }
    public void setDestinationType(DestinationType destinationType) { this.destinationType = destinationType; }

    public DestinationStatus getDestinationStatus() { return destinationStatus; }
    public void setDestinationStatus(DestinationStatus destinationStatus) { this.destinationStatus = destinationStatus; }

    public String getPsp() { return psp; }
    public void setPsp(String psp) { this.psp = psp; }

    public Map<String, Object> getIncomingRequest() { return incomingRequest; }
    public void setIncomingRequest(Map<String, Object> incomingRequest) { this.incomingRequest = incomingRequest; }

    public Map<String, Object> getDestinationResponse() { return destinationResponse; }
    public void setDestinationResponse(Map<String, Object> destinationResponse) { this.destinationResponse = destinationResponse; }

    public String getReference() { return reference; }
    public void setReference(String reference) { this.reference = reference; }

    public String getComment() { return comment; }
    public void setComment(String comment) { this.comment = comment; }

    public String getExchangedTo() { return exchangedTo; }
    public void setExchangedTo(String exchangedTo) { this.exchangedTo = exchangedTo; }

    public BigDecimal getExchangeRate() { return exchangeRate; }
    public void setExchangeRate(BigDecimal exchangeRate) { this.exchangeRate = exchangeRate; }

    public BigDecimal getExchangedAmount() { return exchangedAmount; }
    public void setExchangedAmount(BigDecimal exchangedAmount) { this.exchangedAmount = exchangedAmount; }

    public BigDecimal getExchangeMarkup() { return exchangeMarkup; }
    public void setExchangeMarkup(BigDecimal exchangeMarkup) { this.exchangeMarkup = exchangeMarkup; }

    public Boolean getIsSeychelles() { return isSeychelles; }
    public void setIsSeychelles(Boolean isSeychelles) { this.isSeychelles = isSeychelles; }

    public String getPaymentMethod() { return paymentMethod; }
    public void setPaymentMethod(String paymentMethod) { this.paymentMethod = paymentMethod; }

    public String getPaymentProcessor() { return paymentProcessor; }
    public void setPaymentProcessor(String paymentProcessor) { this.paymentProcessor = paymentProcessor; }

    public String getTradingAccountId() { return tradingAccountId; }
    public void setTradingAccountId(String tradingAccountId) { this.tradingAccountId = tradingAccountId; }

    public BigDecimal getEurAmount() { return eurAmount; }
    public void setEurAmount(BigDecimal eurAmount) { this.eurAmount = eurAmount; }

    public BigDecimal getUsdAmount() { return usdAmount; }
    public void setUsdAmount(BigDecimal usdAmount) { this.usdAmount = usdAmount; }

    public Map<String, Object> getManualBankInfo() { return manualBankInfo; }
    public void setManualBankInfo(Map<String, Object> manualBankInfo) { this.manualBankInfo = manualBankInfo; }

    public LocalDateTime getCreatedAt() { return createdAt; }
    public void setCreatedAt(LocalDateTime createdAt) { this.createdAt = createdAt; }

    public LocalDateTime getUpdatedAt() { return updatedAt; }
    public void setUpdatedAt(LocalDateTime updatedAt) { this.updatedAt = updatedAt; }
}
