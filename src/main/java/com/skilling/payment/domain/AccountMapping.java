package com.skilling.payment.domain;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

import java.time.LocalDateTime;

@Entity
@Table(name = "account_mappings")
public class AccountMapping {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @NotNull
    @Column(name = "customer_id", nullable = false)
    private Long customerId;

    @Column(name = "external_id")
    private Long externalId;

    @NotBlank
    @Column(name = "payment_account_id", nullable = false)
    private String paymentAccountId;

    @Column(name = "trading_account_id")
    private String tradingAccountId;

    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;

    @Column(name = "updated_at")
    private LocalDateTime updatedAt;

    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
        updatedAt = LocalDateTime.now();
    }

    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }

    // Constructors
    public AccountMapping() {}

    public AccountMapping(Long customerId, String paymentAccountId, String tradingAccountId) {
        this.customerId = customerId;
        this.paymentAccountId = paymentAccountId;
        this.tradingAccountId = tradingAccountId;
    }

    // Getters and Setters
    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }

    public Long getCustomerId() { return customerId; }
    public void setCustomerId(Long customerId) { this.customerId = customerId; }

    public Long getExternalId() { return externalId; }
    public void setExternalId(Long externalId) { this.externalId = externalId; }

    public String getPaymentAccountId() { return paymentAccountId; }
    public void setPaymentAccountId(String paymentAccountId) { this.paymentAccountId = paymentAccountId; }

    public String getTradingAccountId() { return tradingAccountId; }
    public void setTradingAccountId(String tradingAccountId) { this.tradingAccountId = tradingAccountId; }

    public LocalDateTime getCreatedAt() { return createdAt; }
    public void setCreatedAt(LocalDateTime createdAt) { this.createdAt = createdAt; }

    public LocalDateTime getUpdatedAt() { return updatedAt; }
    public void setUpdatedAt(LocalDateTime updatedAt) { this.updatedAt = updatedAt; }
}
