package com.skilling.payment.messaging.consumer;

import com.rabbitmq.client.Channel;
import com.skilling.payment.config.RabbitConfig;
import com.skilling.payment.messaging.dto.InactiveAccountFeeMessage;
import com.skilling.payment.service.InactiveAccountFeeService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.math.BigDecimal;

@Component
public class InactiveAccountFeeConsumer {

    private static final Logger logger = LoggerFactory.getLogger(InactiveAccountFeeConsumer.class);

    private final InactiveAccountFeeService inactiveAccountFeeService;

    public InactiveAccountFeeConsumer(InactiveAccountFeeService inactiveAccountFeeService) {
        this.inactiveAccountFeeService = inactiveAccountFeeService;
    }

    @RabbitListener(queues = RabbitConfig.INACTIVE_ACCOUNT_NOTIFICATION_QUEUE)
    public void handleInactiveAccountFee(@Payload InactiveAccountFeeMessage message,
                                       @Header(AmqpHeaders.DELIVERY_TAG) long deliveryTag,
                                       @Header(AmqpHeaders.CHANNEL) Channel channel,
                                       Message amqpMessage) {
        
        logger.info("Received inactive account fee message for customerId: {}, tradingAccountId: {}, feeAmount: {}", 
                   message.customerId(), message.data().tradingAccountId(), message.data().feeAmount());
        
        try {
            // Validate message
            if (!isValidMessage(message)) {
                logger.error("Invalid inactive account fee message: {}", message);
                rejectMessage(channel, deliveryTag, false);
                return;
            }

            // Process the inactive account fee
            processInactiveAccountFee(message);

            // Acknowledge successful processing
            channel.basicAck(deliveryTag, false);
            logger.info("Successfully processed inactive account fee for customerId: {}", message.customerId());

        } catch (Exception e) {
            logger.error("Error processing inactive account fee message for customerId: {}", 
                        message.customerId(), e);
            
            try {
                // Check if this is a retryable error
                if (isRetryableError(e)) {
                    // Reject and requeue for retry
                    channel.basicNack(deliveryTag, false, true);
                    logger.info("Message requeued for retry due to retryable error");
                } else {
                    // Reject without requeue (will go to DLQ)
                    rejectMessage(channel, deliveryTag, false);
                    logger.error("Message rejected and sent to DLQ due to non-retryable error");
                }
            } catch (IOException ioException) {
                logger.error("Failed to handle message rejection", ioException);
            }
        }
    }

    private void processInactiveAccountFee(InactiveAccountFeeMessage message) {
        var data = message.data();
        
        logger.info("Processing inactive account fee: customerId={}, tradingAccountId={}, feeAmount={}, inactiveDays={}", 
                   data.customerId(), data.tradingAccountId(), data.feeAmount(), data.inactiveDays());

        // Validate fee amount is positive
        if (data.feeAmount().compareTo(BigDecimal.ZERO) <= 0) {
            logger.warn("Invalid fee amount: {} for customerId: {}", data.feeAmount(), data.customerId());
            throw new IllegalArgumentException("Fee amount must be positive");
        }

        // Validate account has sufficient balance (if provided)
        if (data.accountInfo().currentBalance() != null) {
            if (data.accountInfo().currentBalance().compareTo(data.feeAmount()) < 0) {
                logger.warn("Insufficient balance for fee deduction: balance={}, fee={}, customerId={}", 
                           data.accountInfo().currentBalance(), data.feeAmount(), data.customerId());
                // Don't throw exception - this might be expected behavior
            }
        }

        // Process the fee transaction
        inactiveAccountFeeService.processInactiveAccountFee(message);
    }

    private boolean isValidMessage(InactiveAccountFeeMessage message) {
        if (message == null || message.data() == null) {
            return false;
        }
        
        var data = message.data();
        return data.customerId() != null 
               && data.tradingAccountId() != null 
               && !data.tradingAccountId().trim().isEmpty()
               && data.currency() != null
               && !data.currency().trim().isEmpty()
               && data.feeAmount() != null
               && data.feeAmount().compareTo(BigDecimal.ZERO) > 0
               && data.inactiveDays() > 0;
    }

    private boolean isRetryableError(Exception e) {
        // Define which errors are retryable
        return e instanceof java.net.ConnectException
               || e instanceof java.net.SocketTimeoutException
               || e instanceof org.springframework.dao.DataAccessResourceFailureException
               || e instanceof org.springframework.dao.TransientDataAccessException;
    }

    private void rejectMessage(Channel channel, long deliveryTag, boolean requeue) throws IOException {
        channel.basicNack(deliveryTag, false, requeue);
    }
}
