package com.skilling.payment.messaging.consumer;

import com.rabbitmq.client.Channel;
import com.skilling.payment.config.RabbitConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Map;

@Component
public class DeadLetterQueueConsumer {

    private static final Logger logger = LoggerFactory.getLogger(DeadLetterQueueConsumer.class);

    @RabbitListener(queues = RabbitConfig.DL_QUEUE)
    public void handleDeadLetterMessage(@Payload String messageBody,
                                      @Header(AmqpHeaders.DELIVERY_TAG) long deliveryTag,
                                      @Header(AmqpHeaders.CHANNEL) Channel channel,
                                      @Header Map<String, Object> headers,
                                      Message amqpMessage) {
        
        logger.error("Received message in Dead Letter Queue. Headers: {}, Body: {}", headers, messageBody);
        
        try {
            // Extract original queue information
            String originalQueue = getOriginalQueue(headers);
            String originalRoutingKey = getOriginalRoutingKey(headers);
            Long customerId = getCustomerId(headers);
            String messageType = getMessageType(headers);
            
            logger.error("Dead letter message details - Original Queue: {}, Routing Key: {}, Customer ID: {}, Message Type: {}", 
                        originalQueue, originalRoutingKey, customerId, messageType);
            
            // Log the failure for monitoring and alerting
            logDeadLetterMessage(originalQueue, originalRoutingKey, customerId, messageType, messageBody, headers);
            
            // Send alert notification if needed
            sendAlertNotification(originalQueue, customerId, messageType, messageBody);
            
            // Acknowledge the message to remove it from DLQ
            channel.basicAck(deliveryTag, false);
            
            logger.info("Dead letter message processed and acknowledged");
            
        } catch (Exception e) {
            logger.error("Error processing dead letter message", e);
            
            try {
                // Reject the message without requeue to prevent infinite loop
                channel.basicNack(deliveryTag, false, false);
            } catch (IOException ioException) {
                logger.error("Failed to reject dead letter message", ioException);
            }
        }
    }

    private String getOriginalQueue(Map<String, Object> headers) {
        // RabbitMQ adds x-death header with information about the original message
        @SuppressWarnings("unchecked")
        var xDeath = (java.util.List<Map<String, Object>>) headers.get("x-death");
        
        if (xDeath != null && !xDeath.isEmpty()) {
            var firstDeath = xDeath.get(0);
            return (String) firstDeath.get("queue");
        }
        
        return "unknown";
    }

    private String getOriginalRoutingKey(Map<String, Object> headers) {
        @SuppressWarnings("unchecked")
        var xDeath = (java.util.List<Map<String, Object>>) headers.get("x-death");
        
        if (xDeath != null && !xDeath.isEmpty()) {
            var firstDeath = xDeath.get(0);
            @SuppressWarnings("unchecked")
            var routingKeys = (java.util.List<String>) firstDeath.get("routing-keys");
            if (routingKeys != null && !routingKeys.isEmpty()) {
                return routingKeys.get(0);
            }
        }
        
        return "unknown";
    }

    private Long getCustomerId(Map<String, Object> headers) {
        Object customerIdObj = headers.get("customerId");
        if (customerIdObj instanceof Number) {
            return ((Number) customerIdObj).longValue();
        } else if (customerIdObj instanceof String) {
            try {
                return Long.parseLong((String) customerIdObj);
            } catch (NumberFormatException e) {
                logger.warn("Invalid customerId format: {}", customerIdObj);
            }
        }
        return null;
    }

    private String getMessageType(Map<String, Object> headers) {
        Object messageType = headers.get("messageType");
        return messageType != null ? messageType.toString() : "unknown";
    }

    private void logDeadLetterMessage(String originalQueue, String originalRoutingKey, Long customerId, 
                                    String messageType, String messageBody, Map<String, Object> headers) {
        
        logger.error("=== DEAD LETTER MESSAGE DETAILS ===");
        logger.error("Original Queue: {}", originalQueue);
        logger.error("Original Routing Key: {}", originalRoutingKey);
        logger.error("Customer ID: {}", customerId);
        logger.error("Message Type: {}", messageType);
        logger.error("Message Body: {}", messageBody);
        logger.error("Headers: {}", headers);
        logger.error("Timestamp: {}", java.time.LocalDateTime.now());
        logger.error("===================================");
        
        // TODO: Store in database for analysis and potential manual reprocessing
        // TODO: Send to monitoring system (e.g., Datadog, New Relic)
    }

    private void sendAlertNotification(String originalQueue, Long customerId, String messageType, String messageBody) {
        try {
            // TODO: Implement alerting mechanism
            // This could be:
            // - Email notification to operations team
            // - Slack/Teams notification
            // - PagerDuty alert for critical messages
            // - Metrics increment for monitoring dashboards
            
            logger.warn("ALERT: Message failed processing and moved to DLQ - Queue: {}, Customer: {}, Type: {}", 
                       originalQueue, customerId, messageType);
            
            // For now, just log the alert
            // In production, you would integrate with your alerting system
            
        } catch (Exception e) {
            logger.error("Failed to send alert notification for dead letter message", e);
        }
    }
}
