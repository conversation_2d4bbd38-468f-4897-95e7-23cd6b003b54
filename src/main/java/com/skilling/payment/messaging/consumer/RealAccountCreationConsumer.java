package com.skilling.payment.messaging.consumer;

import com.rabbitmq.client.Channel;
import com.skilling.payment.config.RabbitConfig;
import com.skilling.payment.messaging.dto.RealAccountCreationMessage;
import com.skilling.payment.service.AccountMappingService;
import com.skilling.payment.service.RealAccountCreationService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;

import java.io.IOException;

@Component
public class RealAccountCreationConsumer {

    private static final Logger logger = LoggerFactory.getLogger(RealAccountCreationConsumer.class);

    private final RealAccountCreationService realAccountCreationService;
    private final AccountMappingService accountMappingService;

    public RealAccountCreationConsumer(RealAccountCreationService realAccountCreationService,
                                     AccountMappingService accountMappingService) {
        this.realAccountCreationService = realAccountCreationService;
        this.accountMappingService = accountMappingService;
    }

    @RabbitListener(queues = RabbitConfig.REAL_ACCOUNT_CREATION_QUEUE)
    public void handleRealAccountCreation(@Payload RealAccountCreationMessage message,
                                        @Header(AmqpHeaders.DELIVERY_TAG) long deliveryTag,
                                        @Header(AmqpHeaders.CHANNEL) Channel channel,
                                        Message amqpMessage) {
        
        logger.info("Received real account creation message for customerId: {}, tradingAccountId: {}", 
                   message.customerId(), message.data().tradingAccountId());
        
        try {
            // Validate message
            if (!isValidMessage(message)) {
                logger.error("Invalid real account creation message: {}", message);
                rejectMessage(channel, deliveryTag, false);
                return;
            }

            // Process the real account creation
            processRealAccountCreation(message);

            // Acknowledge successful processing
            channel.basicAck(deliveryTag, false);
            logger.info("Successfully processed real account creation for customerId: {}", message.customerId());

        } catch (Exception e) {
            logger.error("Error processing real account creation message for customerId: {}", 
                        message.customerId(), e);
            
            try {
                // Check if this is a retryable error
                if (isRetryableError(e)) {
                    // Reject and requeue for retry
                    channel.basicNack(deliveryTag, false, true);
                    logger.info("Message requeued for retry due to retryable error");
                } else {
                    // Reject without requeue (will go to DLQ)
                    rejectMessage(channel, deliveryTag, false);
                    logger.error("Message rejected and sent to DLQ due to non-retryable error");
                }
            } catch (IOException ioException) {
                logger.error("Failed to handle message rejection", ioException);
            }
        }
    }

    private void processRealAccountCreation(RealAccountCreationMessage message) {
        var data = message.data();
        
        // Create or update account mapping
        try {
            var existingMapping = accountMappingService.findByCustomerId(data.customerId());
            
            if (existingMapping.isEmpty()) {
                // Create new account mapping
                var accountMapping = accountMappingService.createAccountMapping(
                        data.customerId(),
                        null, // Will generate payment account ID
                        data.tradingAccountId()
                );
                logger.info("Created new account mapping for customerId: {}, mappingId: {}", 
                           data.customerId(), accountMapping.getId());
            } else {
                // Update existing mapping with trading account ID
                var mapping = existingMapping.get();
                if (mapping.getTradingAccountId() == null || !mapping.getTradingAccountId().equals(data.tradingAccountId())) {
                    accountMappingService.updateAccountMapping(
                            mapping.getId(),
                            mapping.getPaymentAccountId(),
                            data.tradingAccountId()
                    );
                    logger.info("Updated account mapping for customerId: {} with tradingAccountId: {}", 
                               data.customerId(), data.tradingAccountId());
                }
            }

            // Process additional real account creation logic
            realAccountCreationService.processRealAccountCreation(message);

        } catch (IllegalArgumentException e) {
            logger.warn("Account mapping already exists for customerId: {}", data.customerId());
            // This is not an error - account mapping might already exist
            realAccountCreationService.processRealAccountCreation(message);
        }
    }

    private boolean isValidMessage(RealAccountCreationMessage message) {
        if (message == null || message.data() == null) {
            return false;
        }
        
        var data = message.data();
        return data.customerId() != null 
               && data.tradingAccountId() != null 
               && !data.tradingAccountId().trim().isEmpty()
               && data.currency() != null
               && !data.currency().trim().isEmpty();
    }

    private boolean isRetryableError(Exception e) {
        // Define which errors are retryable (e.g., network issues, temporary database issues)
        return e instanceof java.net.ConnectException
               || e instanceof java.net.SocketTimeoutException
               || e instanceof org.springframework.dao.DataAccessResourceFailureException
               || e instanceof org.springframework.dao.TransientDataAccessException;
    }

    private void rejectMessage(Channel channel, long deliveryTag, boolean requeue) throws IOException {
        channel.basicNack(deliveryTag, false, requeue);
    }
}
