package com.skilling.payment.messaging.dto;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Map;

public record InactiveAccountFeeMessage(
        String type,
        Long customerId,
        LocalDateTime created,
        InactiveAccountFeeData data,
        Map<String, Object> headers
) {

    public record InactiveAccountFeeData(
            Long customerId,
            String tradingAccountId,
            String currency,
            BigDecimal feeAmount,
            String reason,
            LocalDateTime lastActivityDate,
            int inactiveDays,
            AccountInfo accountInfo
    ) {}

    public record AccountInfo(
            String accountType,
            String license,
            BigDecimal currentBalance,
            boolean isActive
    ) {}
}
