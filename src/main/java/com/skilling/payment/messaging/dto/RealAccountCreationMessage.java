package com.skilling.payment.messaging.dto;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.time.LocalDateTime;
import java.util.Map;

public record RealAccountCreationMessage(
        String type,
        Long customerId,
        LocalDateTime created,
        RealAccountCreationData data,
        Map<String, Object> headers
) {

    public record RealAccountCreationData(
            Long customerId,
            String tradingAccountId,
            String currency,
            String accountType,
            String license,
            CustomerInfo customerInfo,
            AccountDetails accountDetails
    ) {}

    public record CustomerInfo(
            String email,
            String firstName,
            String lastName,
            String country,
            String phone
    ) {}

    public record AccountDetails(
            String platform,
            String leverage,
            String accountGroup,
            boolean isDemo
    ) {}
}
