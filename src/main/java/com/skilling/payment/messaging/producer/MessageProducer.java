package com.skilling.payment.messaging.producer;

import com.skilling.payment.config.RabbitConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.MessageProperties;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.UUID;

@Service
public class MessageProducer {

    private static final Logger logger = LoggerFactory.getLogger(MessageProducer.class);

    private final RabbitTemplate rabbitTemplate;

    public MessageProducer(RabbitTemplate rabbitTemplate) {
        this.rabbitTemplate = rabbitTemplate;
    }

    public void sendRealAccountCreationMessage(Long customerId, Map<String, Object> data) {
        logger.info("Sending real account creation message for customerId: {}", customerId);
        
        try {
            Map<String, Object> message = createMessage("REAL_ACCOUNT_CREATION", customerId, data);
            
            rabbitTemplate.convertAndSend(
                    RabbitConfig.EXCHANGE_NAME,
                    RabbitConfig.REAL_ACCOUNT_CREATION_ROUTING_KEY,
                    message,
                    messagePostProcessor -> {
                        MessageProperties props = messagePostProcessor.getMessageProperties();
                        props.setMessageId(UUID.randomUUID().toString());
                        props.setTimestamp(new java.util.Date());
                        props.setHeader("customerId", customerId);
                        props.setHeader("messageType", "REAL_ACCOUNT_CREATION");
                        return messagePostProcessor;
                    }
            );
            
            logger.info("Real account creation message sent successfully for customerId: {}", customerId);
        } catch (Exception e) {
            logger.error("Failed to send real account creation message for customerId: {}", customerId, e);
            throw new RuntimeException("Failed to send real account creation message", e);
        }
    }

    public void sendInactiveAccountFeeMessage(Long customerId, Map<String, Object> data) {
        logger.info("Sending inactive account fee message for customerId: {}", customerId);
        
        try {
            Map<String, Object> message = createMessage("INACTIVE_ACCOUNT_FEE", customerId, data);
            
            rabbitTemplate.convertAndSend(
                    RabbitConfig.EXCHANGE_NAME,
                    RabbitConfig.INACTIVE_ACCOUNT_NOTIFICATION_ROUTING_KEY,
                    message,
                    messagePostProcessor -> {
                        MessageProperties props = messagePostProcessor.getMessageProperties();
                        props.setMessageId(UUID.randomUUID().toString());
                        props.setTimestamp(new java.util.Date());
                        props.setHeader("customerId", customerId);
                        props.setHeader("messageType", "INACTIVE_ACCOUNT_FEE");
                        return messagePostProcessor;
                    }
            );
            
            logger.info("Inactive account fee message sent successfully for customerId: {}", customerId);
        } catch (Exception e) {
            logger.error("Failed to send inactive account fee message for customerId: {}", customerId, e);
            throw new RuntimeException("Failed to send inactive account fee message", e);
        }
    }

    public void sendMessage(String routingKey, Long customerId, Map<String, Object> data, Map<String, Object> headers) {
        logger.info("Sending message with routing key: {} for customerId: {}", routingKey, customerId);
        
        try {
            String messageType = (String) headers.getOrDefault("messageType", "GENERIC");
            Map<String, Object> message = createMessage(messageType, customerId, data);
            
            rabbitTemplate.convertAndSend(
                    RabbitConfig.EXCHANGE_NAME,
                    routingKey,
                    message,
                    messagePostProcessor -> {
                        MessageProperties props = messagePostProcessor.getMessageProperties();
                        props.setMessageId(UUID.randomUUID().toString());
                        props.setTimestamp(new java.util.Date());
                        props.setHeader("customerId", customerId);
                        
                        // Add custom headers
                        headers.forEach((key, value) -> props.setHeader(key, value));
                        
                        return messagePostProcessor;
                    }
            );
            
            logger.info("Message sent successfully with routing key: {} for customerId: {}", routingKey, customerId);
        } catch (Exception e) {
            logger.error("Failed to send message with routing key: {} for customerId: {}", routingKey, customerId, e);
            throw new RuntimeException("Failed to send message", e);
        }
    }

    public void sendTransactionStatusUpdate(Long customerId, Long transactionId, String status, Map<String, Object> additionalData) {
        logger.info("Sending transaction status update for customerId: {}, transactionId: {}, status: {}", 
                   customerId, transactionId, status);
        
        try {
            Map<String, Object> data = Map.of(
                    "transactionId", transactionId,
                    "status", status,
                    "additionalData", additionalData != null ? additionalData : Map.of()
            );
            
            sendMessage(
                    "transaction.status.updated",
                    customerId,
                    data,
                    Map.of("messageType", "TRANSACTION_STATUS_UPDATE")
            );
            
            logger.info("Transaction status update sent for customerId: {}, transactionId: {}", customerId, transactionId);
        } catch (Exception e) {
            logger.error("Failed to send transaction status update for customerId: {}, transactionId: {}", 
                        customerId, transactionId, e);
            // Don't throw exception for status updates to avoid breaking main flow
        }
    }

    public void sendPaymentNotification(Long customerId, String notificationType, Map<String, Object> data) {
        logger.info("Sending payment notification: {} for customerId: {}", notificationType, customerId);
        
        try {
            sendMessage(
                    "payment.notification." + notificationType.toLowerCase(),
                    customerId,
                    data,
                    Map.of("messageType", "PAYMENT_NOTIFICATION", "notificationType", notificationType)
            );
            
            logger.info("Payment notification sent: {} for customerId: {}", notificationType, customerId);
        } catch (Exception e) {
            logger.error("Failed to send payment notification: {} for customerId: {}", notificationType, customerId, e);
            // Don't throw exception for notifications to avoid breaking main flow
        }
    }

    private Map<String, Object> createMessage(String type, Long customerId, Map<String, Object> data) {
        return Map.of(
                "type", type,
                "customerId", customerId,
                "created", LocalDateTime.now(),
                "data", data,
                "headers", Map.of(
                        "source", "payment-service",
                        "version", "1.0"
                )
        );
    }
}
