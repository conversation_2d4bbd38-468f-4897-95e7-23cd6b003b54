package com.skilling.payment.messaging.health;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.boot.actuator.health.Health;
import org.springframework.boot.actuator.health.HealthIndicator;
import org.springframework.stereotype.Component;

@Component
public class MessagingHealthIndicator implements HealthIndicator {

    private static final Logger logger = LoggerFactory.getLogger(MessagingHealthIndicator.class);

    private final RabbitTemplate rabbitTemplate;

    public MessagingHealthIndicator(RabbitTemplate rabbitTemplate) {
        this.rabbitTemplate = rabbitTemplate;
    }

    @Override
    public Health health() {
        try {
            // Test RabbitMQ connection by checking if we can get connection factory
            var connectionFactory = rabbitTemplate.getConnectionFactory();
            
            if (connectionFactory != null) {
                // Try to create a connection to test connectivity
                try (var connection = connectionFactory.createConnection()) {
                    if (connection.isOpen()) {
                        logger.debug("RabbitMQ connection is healthy");
                        return Health.up()
                                .withDetail("rabbitmq", "Connected")
                                .withDetail("host", connectionFactory.getHost())
                                .withDetail("port", connectionFactory.getPort())
                                .withDetail("virtualHost", connectionFactory.getVirtualHost())
                                .build();
                    }
                }
            }
            
            logger.warn("RabbitMQ connection is not available");
            return Health.down()
                    .withDetail("rabbitmq", "Connection not available")
                    .build();
                    
        } catch (Exception e) {
            logger.error("RabbitMQ health check failed", e);
            return Health.down()
                    .withDetail("rabbitmq", "Connection failed")
                    .withDetail("error", e.getMessage())
                    .build();
        }
    }
}
