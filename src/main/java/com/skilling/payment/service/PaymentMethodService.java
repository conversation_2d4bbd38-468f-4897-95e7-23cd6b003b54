package com.skilling.payment.service;

import com.skilling.payment.domain.PaymentMethod;
import com.skilling.payment.repository.PaymentMethodRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

@Service
@Transactional(readOnly = true)
public class PaymentMethodService {

    private static final Logger logger = LoggerFactory.getLogger(PaymentMethodService.class);

    private final PaymentMethodRepository paymentMethodRepository;

    public PaymentMethodService(PaymentMethodRepository paymentMethodRepository) {
        this.paymentMethodRepository = paymentMethodRepository;
    }

    public List<PaymentMethod> getAllEnabledMethods() {
        logger.info("Fetching all enabled payment methods");
        return paymentMethodRepository.findByEnabledTrue();
    }

    public List<PaymentMethod> getMethodsForFirstDeposit() {
        logger.info("Fetching payment methods available for first deposit");
        return paymentMethodRepository.findByAvailableForFirstDepositTrueAndEnabledTrue();
    }

    public Map<String, Object> getAllMethodsV2(String country, String currency) {
        logger.info("Fetching all payment methods V2 for country: {}, currency: {}", country, currency);
        
        List<PaymentMethod> methods = getAllEnabledMethods();
        
        return Map.of(
                "success", true,
                "methods", methods,
                "country", country != null ? country : "ALL",
                "currency", currency != null ? currency : "ALL"
        );
    }

    public Map<String, Object> getMethodsForFirstDepositV2(String country, String currency) {
        logger.info("Fetching first deposit payment methods V2 for country: {}, currency: {}", country, currency);
        
        List<PaymentMethod> methods = getMethodsForFirstDeposit();
        
        return Map.of(
                "success", true,
                "methods", methods,
                "country", country != null ? country : "ALL",
                "currency", currency != null ? currency : "ALL",
                "firstDepositOnly", true
        );
    }

    public List<PaymentMethod> getMethodsByType(String type) {
        logger.info("Fetching payment methods by type: {}", type);
        return paymentMethodRepository.findByType(type);
    }

    public List<PaymentMethod> getPrimaryMethods() {
        logger.info("Fetching primary payment methods");
        return paymentMethodRepository.findByPrimaryTrue();
    }
}
