package com.skilling.payment.service;

import com.skilling.payment.controller.TransactionController.CreateManualTransactionRequest;
import com.skilling.payment.controller.TransactionController.PatchManualTransactionRequest;
import com.skilling.payment.domain.Transaction;
import com.skilling.payment.domain.enums.TransactionStatus;
import com.skilling.payment.domain.enums.TransactionType;
import com.skilling.payment.repository.TransactionRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

@Service
@Transactional
public class TransactionService {

    private static final Logger logger = LoggerFactory.getLogger(TransactionService.class);

    private final TransactionRepository transactionRepository;

    public TransactionService(TransactionRepository transactionRepository) {
        this.transactionRepository = transactionRepository;
    }

    @Transactional(readOnly = true)
    public List<Long> getFirstDepositors() {
        logger.info("Fetching first depositors");
        return transactionRepository.findFirstDepositors();
    }

    @Transactional(readOnly = true)
    public Optional<Transaction> getTransactionById(Long id) {
        logger.info("Fetching transaction by id: {}", id);
        return transactionRepository.findById(id);
    }

    @Transactional(readOnly = true)
    public Optional<Transaction> getCustomerTransactionById(Long customerId, Long id) {
        logger.info("Fetching transaction by customerId: {} and id: {}", customerId, id);
        return transactionRepository.findByCustomerIdAndId(customerId, id);
    }

    @Transactional(readOnly = true)
    public Optional<Transaction> getCustomerTransactionByReference(Long customerId, String reference) {
        logger.info("Fetching transaction by customerId: {} and reference: {}", customerId, reference);
        return transactionRepository.findByCustomerIdAndReference(customerId, reference);
    }

    @Transactional(readOnly = true)
    public Page<Transaction> searchTransactions(Long customerId, String status, String transactionType, 
                                              String license, Pageable pageable) {
        logger.info("Searching transactions with customerId: {}, status: {}, type: {}, license: {}", 
                   customerId, status, transactionType, license);
        
        if (customerId != null) {
            return transactionRepository.findByCustomerId(customerId, pageable);
        }
        
        return transactionRepository.findAll(pageable);
    }

    public Transaction createManualTransaction(Long customerId, String accountId, 
                                             CreateManualTransactionRequest request) {
        logger.info("Creating manual transaction for customerId: {}, accountId: {}", customerId, accountId);
        
        Transaction transaction = new Transaction();
        transaction.setCustomerId(customerId);
        transaction.setAmount(new BigDecimal(request.amount()));
        transaction.setCurrency(request.currency());
        transaction.setTransactionType(TransactionType.valueOf(request.transactionType()));
        transaction.setStatus(TransactionStatus.REQUESTED);
        transaction.setComment(request.comment());
        transaction.setReference(request.reference());
        transaction.setExternalId(generateExternalId());
        transaction.setPsp("MANUAL");
        transaction.setTradingAccountId(accountId);
        
        Transaction savedTransaction = transactionRepository.save(transaction);
        logger.info("Created manual transaction with id: {}", savedTransaction.getId());
        
        return savedTransaction;
    }

    public Optional<Transaction> patchManualTransaction(Long customerId, Long id, 
                                                      PatchManualTransactionRequest request) {
        logger.info("Patching manual transaction for customerId: {}, id: {}", customerId, id);
        
        return transactionRepository.findByCustomerIdAndId(customerId, id)
                .map(transaction -> {
                    if (request.status() != null) {
                        transaction.setStatus(TransactionStatus.valueOf(request.status()));
                    }
                    if (request.comment() != null) {
                        transaction.setComment(request.comment());
                    }
                    
                    Transaction savedTransaction = transactionRepository.save(transaction);
                    logger.info("Patched manual transaction with id: {}", savedTransaction.getId());
                    
                    return savedTransaction;
                });
    }

    public Transaction createTransaction(Transaction transaction) {
        logger.info("Creating transaction for customerId: {}", transaction.getCustomerId());
        
        if (transactionRepository.existsByExternalId(transaction.getExternalId())) {
            logger.warn("Transaction with externalId {} already exists", transaction.getExternalId());
            throw new IllegalArgumentException("Transaction with external ID already exists");
        }
        
        Transaction savedTransaction = transactionRepository.save(transaction);
        logger.info("Created transaction with id: {}", savedTransaction.getId());
        
        return savedTransaction;
    }

    @Transactional(readOnly = true)
    public Optional<Transaction> findByExternalId(String externalId) {
        return transactionRepository.findByExternalId(externalId);
    }

    @Transactional(readOnly = true)
    public Long countDepositsByCustomerId(Long customerId) {
        return transactionRepository.countDepositsByCustomerId(customerId);
    }

    private String generateExternalId() {
        return "manual_" + System.currentTimeMillis();
    }
}
