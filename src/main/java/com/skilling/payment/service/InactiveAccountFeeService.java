package com.skilling.payment.service;

import com.skilling.payment.config.PaymentProperties;
import com.skilling.payment.domain.Transaction;
import com.skilling.payment.domain.enums.DestinationStatus;
import com.skilling.payment.domain.enums.DestinationType;
import com.skilling.payment.domain.enums.TransactionStatus;
import com.skilling.payment.domain.enums.TransactionType;
import com.skilling.payment.external.TradingBalanceServiceClient;
import com.skilling.payment.messaging.dto.InactiveAccountFeeMessage;
import com.skilling.payment.messaging.producer.MessageProducer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Map;

@Service
@Transactional
public class InactiveAccountFeeService {

    private static final Logger logger = LoggerFactory.getLogger(InactiveAccountFeeService.class);

    private final TransactionService transactionService;
    private final TradingBalanceServiceClient tradingBalanceService;
    private final MessageProducer messageProducer;
    private final PaymentProperties paymentProperties;

    public InactiveAccountFeeService(TransactionService transactionService,
                                   TradingBalanceServiceClient tradingBalanceService,
                                   MessageProducer messageProducer,
                                   PaymentProperties paymentProperties) {
        this.transactionService = transactionService;
        this.tradingBalanceService = tradingBalanceService;
        this.messageProducer = messageProducer;
        this.paymentProperties = paymentProperties;
    }

    public void processInactiveAccountFee(InactiveAccountFeeMessage message) {
        var data = message.data();
        
        logger.info("Processing inactive account fee for customerId: {}, tradingAccountId: {}, feeAmount: {}", 
                   data.customerId(), data.tradingAccountId(), data.feeAmount());

        try {
            // Validate account balance if needed
            BigDecimal accountBalance = validateAccountBalance(data);

            // Determine final fee amount based on currency and configuration
            BigDecimal finalFeeAmount = calculateFinalFeeAmount(data);

            // Create fee transaction
            Transaction feeTransaction = createInactiveAccountFeeTransaction(data, finalFeeAmount, accountBalance);

            // Send notification about fee deduction
            sendFeeDeductionNotification(message, feeTransaction);

            logger.info("Successfully processed inactive account fee for customerId: {}, transactionId: {}", 
                       data.customerId(), feeTransaction.getId());

        } catch (Exception e) {
            logger.error("Error processing inactive account fee for customerId: {}", data.customerId(), e);
            
            // Create failed fee transaction for audit
            createFailedFeeTransaction(data, e.getMessage());
            
            throw e;
        }
    }

    private BigDecimal validateAccountBalance(InactiveAccountFeeMessage.InactiveAccountFeeData data) {
        logger.info("Validating account balance for customerId: {}, tradingAccountId: {}", 
                   data.customerId(), data.tradingAccountId());

        try {
            // Get current account balance from trading balance service
            var accountBalance = tradingBalanceService.getAccountBalance(
                    data.tradingAccountId(), 
                    data.customerId(), 
                    Map.of("x-function", "inactive-account-fee")
            ).block();

            if (accountBalance != null) {
                BigDecimal currentBalance = accountBalance.balance();
                logger.info("Current account balance: {} {} for customerId: {}", 
                           currentBalance, data.currency(), data.customerId());

                // Check if balance is sufficient for fee deduction
                if (currentBalance.compareTo(data.feeAmount()) < 0) {
                    logger.warn("Insufficient balance for fee deduction: balance={}, fee={}, customerId={}", 
                               currentBalance, data.feeAmount(), data.customerId());
                    // Continue processing - the trading system will handle insufficient balance
                }

                return currentBalance;
            } else {
                logger.warn("Could not retrieve account balance for customerId: {}", data.customerId());
                return data.accountInfo().currentBalance(); // Use balance from message
            }
        } catch (Exception e) {
            logger.error("Error validating account balance for customerId: {}", data.customerId(), e);
            return data.accountInfo().currentBalance(); // Use balance from message as fallback
        }
    }

    private BigDecimal calculateFinalFeeAmount(InactiveAccountFeeMessage.InactiveAccountFeeData data) {
        // Check if there's a currency-specific fee amount
        Map<String, Integer> currencyFees = paymentProperties.fees().currencyToInactiveAccountFee();
        
        if (currencyFees != null && currencyFees.containsKey(data.currency())) {
            BigDecimal currencySpecificFee = new BigDecimal(currencyFees.get(data.currency()));
            logger.info("Using currency-specific fee amount: {} {} for customerId: {}", 
                       currencySpecificFee, data.currency(), data.customerId());
            return currencySpecificFee;
        }

        // Use the fee amount from the message
        logger.info("Using message fee amount: {} {} for customerId: {}", 
                   data.feeAmount(), data.currency(), data.customerId());
        return data.feeAmount();
    }

    private Transaction createInactiveAccountFeeTransaction(InactiveAccountFeeMessage.InactiveAccountFeeData data, 
                                                          BigDecimal feeAmount, 
                                                          BigDecimal accountBalance) {
        logger.info("Creating inactive account fee transaction for customerId: {}", data.customerId());

        Transaction transaction = new Transaction();
        transaction.setCustomerId(data.customerId());
        transaction.setAmount(feeAmount);
        transaction.setCurrency(data.currency());
        transaction.setStatus(TransactionStatus.FINISHED);
        transaction.setTransactionType(TransactionType.INACTIVE_ACCOUNT_FEE);
        transaction.setExternalId("inactive_fee_" + data.tradingAccountId() + "_" + System.currentTimeMillis());
        transaction.setPsp("INTERNAL");
        transaction.setDestinationType(DestinationType.XSTATION);
        transaction.setDestinationStatus(DestinationStatus.SUCCESSFUL);
        transaction.setTradingAccountId(data.tradingAccountId());
        transaction.setComment("Inactive account fee - " + data.inactiveDays() + " days inactive");
        transaction.setReference("INACTIVE_ACCOUNT_FEE");

        // Add detailed information to incoming request
        Map<String, Object> requestData = Map.of(
                "reason", data.reason(),
                "lastActivityDate", data.lastActivityDate().toString(),
                "inactiveDays", data.inactiveDays(),
                "accountBalance", accountBalance != null ? accountBalance : BigDecimal.ZERO,
                "accountInfo", data.accountInfo()
        );
        transaction.setIncomingRequest(requestData);

        Transaction savedTransaction = transactionService.createTransaction(transaction);
        logger.info("Created inactive account fee transaction with id: {}", savedTransaction.getId());

        return savedTransaction;
    }

    private void createFailedFeeTransaction(InactiveAccountFeeMessage.InactiveAccountFeeData data, String errorMessage) {
        logger.info("Creating failed fee transaction for audit purposes, customerId: {}", data.customerId());

        try {
            Transaction transaction = new Transaction();
            transaction.setCustomerId(data.customerId());
            transaction.setAmount(data.feeAmount());
            transaction.setCurrency(data.currency());
            transaction.setStatus(TransactionStatus.FAILED);
            transaction.setTransactionType(TransactionType.INACTIVE_ACCOUNT_FEE);
            transaction.setExternalId("failed_inactive_fee_" + data.tradingAccountId() + "_" + System.currentTimeMillis());
            transaction.setPsp("INTERNAL");
            transaction.setDestinationType(DestinationType.XSTATION);
            transaction.setDestinationStatus(DestinationStatus.FAILED);
            transaction.setTradingAccountId(data.tradingAccountId());
            transaction.setComment("Failed inactive account fee: " + errorMessage);
            transaction.setReference("FAILED_INACTIVE_ACCOUNT_FEE");

            Map<String, Object> requestData = Map.of(
                    "reason", data.reason(),
                    "inactiveDays", data.inactiveDays(),
                    "error", errorMessage
            );
            transaction.setIncomingRequest(requestData);

            transactionService.createTransaction(transaction);
            logger.info("Created failed fee transaction for audit");
        } catch (Exception e) {
            logger.error("Failed to create audit transaction for failed fee", e);
        }
    }

    private void sendFeeDeductionNotification(InactiveAccountFeeMessage message, Transaction feeTransaction) {
        logger.info("Sending fee deduction notification for customerId: {}", message.customerId());

        try {
            Map<String, Object> notificationData = Map.of(
                    "customerId", message.customerId(),
                    "tradingAccountId", message.data().tradingAccountId(),
                    "feeAmount", feeTransaction.getAmount(),
                    "currency", feeTransaction.getCurrency(),
                    "transactionId", feeTransaction.getId(),
                    "inactiveDays", message.data().inactiveDays(),
                    "reason", message.data().reason()
            );

            messageProducer.sendMessage(
                    "account.fee.deducted",
                    message.customerId(),
                    notificationData,
                    Map.of("messageType", "INACTIVE_ACCOUNT_FEE_DEDUCTED")
            );

            logger.info("Fee deduction notification sent for customerId: {}", message.customerId());
        } catch (Exception e) {
            logger.error("Failed to send fee deduction notification for customerId: {}", message.customerId(), e);
            // Don't fail the entire process for notification failure
        }
    }
}
