package com.skilling.payment.service;

import com.skilling.payment.domain.AccountMapping;
import com.skilling.payment.repository.AccountMappingRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Service
@Transactional
public class AccountMappingService {

    private static final Logger logger = LoggerFactory.getLogger(AccountMappingService.class);

    private final AccountMappingRepository accountMappingRepository;

    public AccountMappingService(AccountMappingRepository accountMappingRepository) {
        this.accountMappingRepository = accountMappingRepository;
    }

    @Transactional(readOnly = true)
    public Optional<AccountMapping> findByCustomerId(Long customerId) {
        logger.info("Finding account mapping for customerId: {}", customerId);
        return accountMappingRepository.findByCustomerId(customerId);
    }

    @Transactional(readOnly = true)
    public List<AccountMapping> findAllByCustomerId(Long customerId) {
        logger.info("Finding all account mappings for customerId: {}", customerId);
        return accountMappingRepository.findAllByCustomerId(customerId);
    }

    @Transactional(readOnly = true)
    public Optional<AccountMapping> findByPaymentAccountId(String paymentAccountId) {
        logger.info("Finding account mapping for paymentAccountId: {}", paymentAccountId);
        return accountMappingRepository.findByPaymentAccountId(paymentAccountId);
    }

    @Transactional(readOnly = true)
    public Optional<AccountMapping> findByExternalId(Long externalId) {
        logger.info("Finding account mapping for externalId: {}", externalId);
        return accountMappingRepository.findByExternalId(externalId);
    }

    public AccountMapping createAccountMapping(Long customerId, String paymentAccountId, String tradingAccountId) {
        logger.info("Creating account mapping for customerId: {}", customerId);
        
        if (accountMappingRepository.existsByCustomerId(customerId)) {
            logger.warn("Account mapping already exists for customerId: {}", customerId);
            throw new IllegalArgumentException("Account mapping already exists for customer");
        }
        
        AccountMapping mapping = new AccountMapping();
        mapping.setCustomerId(customerId);
        mapping.setPaymentAccountId(paymentAccountId != null ? paymentAccountId : generatePaymentAccountId());
        mapping.setTradingAccountId(tradingAccountId);
        mapping.setExternalId(System.currentTimeMillis()); // Simple external ID generation
        
        AccountMapping savedMapping = accountMappingRepository.save(mapping);
        logger.info("Created account mapping with id: {}", savedMapping.getId());
        
        return savedMapping;
    }

    public AccountMapping updateAccountMapping(Long id, String paymentAccountId, String tradingAccountId) {
        logger.info("Updating account mapping with id: {}", id);
        
        return accountMappingRepository.findById(id)
                .map(mapping -> {
                    if (paymentAccountId != null) {
                        mapping.setPaymentAccountId(paymentAccountId);
                    }
                    if (tradingAccountId != null) {
                        mapping.setTradingAccountId(tradingAccountId);
                    }
                    
                    AccountMapping savedMapping = accountMappingRepository.save(mapping);
                    logger.info("Updated account mapping with id: {}", savedMapping.getId());
                    
                    return savedMapping;
                })
                .orElseThrow(() -> new IllegalArgumentException("Account mapping not found"));
    }

    public void deleteAccountMapping(Long id) {
        logger.info("Deleting account mapping with id: {}", id);
        
        if (!accountMappingRepository.existsById(id)) {
            throw new IllegalArgumentException("Account mapping not found");
        }
        
        accountMappingRepository.deleteById(id);
        logger.info("Deleted account mapping with id: {}", id);
    }

    @Transactional(readOnly = true)
    public boolean existsByCustomerId(Long customerId) {
        return accountMappingRepository.existsByCustomerId(customerId);
    }

    @Transactional(readOnly = true)
    public boolean existsByPaymentAccountId(String paymentAccountId) {
        return accountMappingRepository.existsByPaymentAccountId(paymentAccountId);
    }

    private String generatePaymentAccountId() {
        return "PAY_" + UUID.randomUUID().toString().replace("-", "").substring(0, 16).toUpperCase();
    }
}
