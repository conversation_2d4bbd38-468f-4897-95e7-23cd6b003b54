package com.skilling.payment.config;

import org.springframework.amqp.core.*;
import org.springframework.amqp.rabbit.config.SimpleRabbitListenerContainerFactory;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.amqp.support.converter.Jackson2JsonMessageConverter;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class RabbitConfig {

    public static final String EXCHANGE_NAME = "payment.exchange";
    public static final String DL_EXCHANGE_NAME = "payment.dlx";
    
    public static final String REAL_ACCOUNT_CREATION_QUEUE = "payment.real-account-creation";
    public static final String INACTIVE_ACCOUNT_NOTIFICATION_QUEUE = "payment.inactive-account-notification";
    public static final String DL_QUEUE = "payment.dlq";
    
    public static final String REAL_ACCOUNT_CREATION_ROUTING_KEY = "real-account-creation";
    public static final String INACTIVE_ACCOUNT_NOTIFICATION_ROUTING_KEY = "inactive-account-notification";

    @Bean
    public TopicExchange exchange() {
        return new TopicExchange(EXCHANGE_NAME, true, false);
    }

    @Bean
    public DirectExchange deadLetterExchange() {
        return new DirectExchange(DL_EXCHANGE_NAME, true, false);
    }

    @Bean
    public Queue realAccountCreationQueue() {
        return QueueBuilder.durable(REAL_ACCOUNT_CREATION_QUEUE)
                .withArgument("x-dead-letter-exchange", DL_EXCHANGE_NAME)
                .withArgument("x-dead-letter-routing-key", "")
                .build();
    }

    @Bean
    public Queue inactiveAccountNotificationQueue() {
        return QueueBuilder.durable(INACTIVE_ACCOUNT_NOTIFICATION_QUEUE)
                .withArgument("x-dead-letter-exchange", DL_EXCHANGE_NAME)
                .withArgument("x-dead-letter-routing-key", "")
                .build();
    }

    @Bean
    public Queue deadLetterQueue() {
        return QueueBuilder.durable(DL_QUEUE).build();
    }

    @Bean
    public Binding realAccountCreationBinding() {
        return BindingBuilder
                .bind(realAccountCreationQueue())
                .to(exchange())
                .with(REAL_ACCOUNT_CREATION_ROUTING_KEY);
    }

    @Bean
    public Binding inactiveAccountNotificationBinding() {
        return BindingBuilder
                .bind(inactiveAccountNotificationQueue())
                .to(exchange())
                .with(INACTIVE_ACCOUNT_NOTIFICATION_ROUTING_KEY);
    }

    @Bean
    public Binding deadLetterBinding() {
        return BindingBuilder
                .bind(deadLetterQueue())
                .to(deadLetterExchange())
                .with("");
    }

    @Bean
    public Jackson2JsonMessageConverter messageConverter() {
        return new Jackson2JsonMessageConverter();
    }

    @Bean
    public RabbitTemplate rabbitTemplate(ConnectionFactory connectionFactory) {
        RabbitTemplate template = new RabbitTemplate(connectionFactory);
        template.setMessageConverter(messageConverter());
        template.setMandatory(true);
        return template;
    }

    @Bean
    public SimpleRabbitListenerContainerFactory rabbitListenerContainerFactory(
            ConnectionFactory connectionFactory) {
        SimpleRabbitListenerContainerFactory factory = new SimpleRabbitListenerContainerFactory();
        factory.setConnectionFactory(connectionFactory);
        factory.setMessageConverter(messageConverter());
        factory.setAcknowledgeMode(AcknowledgeMode.MANUAL);
        factory.setDefaultRequeueRejected(false);
        return factory;
    }
}
