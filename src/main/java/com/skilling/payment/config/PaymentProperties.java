package com.skilling.payment.config;

import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.Map;

@ConfigurationProperties(prefix = "payment")
public record PaymentProperties(
    String environment,
    JwtProperties jwt,
    ExternalServicesProperties externalServices,
    DevcodeProperties devcode,
    PraxisProperties praxis,
    FeesProperties fees,
    MessagingProperties messaging
) {

    public record JwtProperties(
        String signingKey,
        boolean ignoreExpiration
    ) {}

    public record ExternalServicesProperties(
        ServiceProperties customerService,
        ServiceProperties tradingBalanceService,
        ServiceProperties tradingAccountService,
        ServiceProperties partnerService,
        ServiceProperties exchangeRateService,
        ServiceProperties devcodeService
    ) {}

    public record ServiceProperties(
        String baseUrl
    ) {}

    public record DevcodeProperties(
        String merchantId,
        String seychellesMerchantId
    ) {}

    public record PraxisProperties(
        String domain,
        String callbackUrlRoot,
        String authHeader,
        long cancelWithdrawalDelayMs,
        PraxisLicenseProperties fsa,
        PraxisLicenseProperties cysec
    ) {}

    public record PraxisLicenseProperties(
        String merchantId,
        String merchantSecret,
        String applicationKey
    ) {}

    public record FeesProperties(
        int defaultInactiveAccountFee,
        Map<String, Integer> currencyToInactiveAccountFee
    ) {}

    public record MessagingProperties(
        boolean enabled,
        int retryAttempts,
        long retryDelayMs,
        boolean deadLetterEnabled
    ) {}
}
