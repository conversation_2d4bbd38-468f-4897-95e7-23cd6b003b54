package com.skilling.payment.config;

import io.netty.channel.ChannelOption;
import io.netty.handler.timeout.ReadTimeoutHandler;
import io.netty.handler.timeout.WriteTimeoutHandler;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.reactive.ReactorClientHttpConnector;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.netty.http.client.HttpClient;

import java.time.Duration;
import java.util.concurrent.TimeUnit;

@Configuration
public class WebClientConfig {

    @Bean
    public WebClient.Builder webClientBuilder() {
        HttpClient httpClient = HttpClient.create()
                .option(ChannelOption.CONNECT_TIMEOUT_MILLIS, 30000)
                .responseTimeout(Duration.ofSeconds(30))
                .doOnConnected(conn ->
                        conn.addHandlerLast(new ReadTimeoutHandler(30, TimeUnit.SECONDS))
                                .addHandlerLast(new WriteTimeoutHandler(30, TimeUnit.SECONDS)));

        return WebClient.builder()
                .clientConnector(new ReactorClientHttpConnector(httpClient))
                .codecs(configurer -> configurer.defaultCodecs().maxInMemorySize(1024 * 1024));
    }

    @Bean
    public WebClient customerServiceWebClient(WebClient.Builder builder, PaymentProperties properties) {
        return builder
                .baseUrl(properties.externalServices().customerService().baseUrl())
                .build();
    }

    @Bean
    public WebClient tradingBalanceServiceWebClient(WebClient.Builder builder, PaymentProperties properties) {
        return builder
                .baseUrl(properties.externalServices().tradingBalanceService().baseUrl())
                .build();
    }

    @Bean
    public WebClient tradingAccountServiceWebClient(WebClient.Builder builder, PaymentProperties properties) {
        return builder
                .baseUrl(properties.externalServices().tradingAccountService().baseUrl())
                .build();
    }

    @Bean
    public WebClient partnerServiceWebClient(WebClient.Builder builder, PaymentProperties properties) {
        return builder
                .baseUrl(properties.externalServices().partnerService().baseUrl())
                .build();
    }

    @Bean
    public WebClient exchangeRateServiceWebClient(WebClient.Builder builder, PaymentProperties properties) {
        return builder
                .baseUrl(properties.externalServices().exchangeRateService().baseUrl())
                .build();
    }

    @Bean
    public WebClient devcodeServiceWebClient(WebClient.Builder builder, PaymentProperties properties) {
        return builder
                .baseUrl(properties.externalServices().devcodeService().baseUrl())
                .build();
    }

    @Bean
    public WebClient praxisWebClient(WebClient.Builder builder, PaymentProperties properties) {
        return builder
                .baseUrl(properties.praxis().domain())
                .build();
    }
}
