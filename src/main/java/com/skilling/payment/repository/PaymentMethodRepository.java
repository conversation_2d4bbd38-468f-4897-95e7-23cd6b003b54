package com.skilling.payment.repository;

import com.skilling.payment.domain.PaymentMethod;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface PaymentMethodRepository extends JpaRepository<PaymentMethod, Long> {

    List<PaymentMethod> findByEnabledTrue();

    List<PaymentMethod> findByAvailableForFirstDepositTrueAndEnabledTrue();

    List<PaymentMethod> findByType(String type);

    List<PaymentMethod> findByPrimaryTrue();
}
