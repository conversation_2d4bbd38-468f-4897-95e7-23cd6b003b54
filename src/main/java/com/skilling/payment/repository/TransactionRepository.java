package com.skilling.payment.repository;

import com.skilling.payment.domain.Transaction;
import com.skilling.payment.domain.enums.TransactionType;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public interface TransactionRepository extends JpaRepository<Transaction, Long> {

    Optional<Transaction> findByExternalId(String externalId);

    List<Transaction> findByCustomerId(Long customerId);

    Page<Transaction> findByCustomerId(Long customerId, Pageable pageable);

    Optional<Transaction> findByCustomerIdAndId(Long customerId, Long id);

    Optional<Transaction> findByCustomerIdAndReference(Long customerId, String reference);

    List<Transaction> findByTransactionType(TransactionType transactionType);

    @Query("SELECT t FROM Transaction t WHERE t.customerId = :customerId AND t.transactionType = :transactionType")
    List<Transaction> findByCustomerIdAndTransactionType(@Param("customerId") Long customerId, 
                                                        @Param("transactionType") TransactionType transactionType);

    @Query("SELECT t FROM Transaction t WHERE t.createdAt BETWEEN :startDate AND :endDate")
    List<Transaction> findByCreatedAtBetween(@Param("startDate") LocalDateTime startDate, 
                                           @Param("endDate") LocalDateTime endDate);

    @Query("SELECT DISTINCT t.customerId FROM Transaction t WHERE t.transactionType = 'DEPOSIT' " +
           "AND t.customerId NOT IN (SELECT t2.customerId FROM Transaction t2 WHERE t2.transactionType = 'DEPOSIT' AND t2.id < t.id)")
    List<Long> findFirstDepositors();

    @Query("SELECT COUNT(t) FROM Transaction t WHERE t.customerId = :customerId AND t.transactionType = 'DEPOSIT'")
    Long countDepositsByCustomerId(@Param("customerId") Long customerId);

    boolean existsByExternalId(String externalId);
}
