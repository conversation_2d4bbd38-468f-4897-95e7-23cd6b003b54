package com.skilling.payment.repository;

import com.skilling.payment.domain.AccountMapping;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface AccountMappingRepository extends JpaRepository<AccountMapping, Long> {

    Optional<AccountMapping> findByCustomerId(Long customerId);

    List<AccountMapping> findAllByCustomerId(Long customerId);

    Optional<AccountMapping> findByPaymentAccountId(String paymentAccountId);

    Optional<AccountMapping> findByExternalId(Long externalId);

    boolean existsByCustomerId(Long customerId);

    boolean existsByPaymentAccountId(String paymentAccountId);
}
