package com.skilling.payment.psp.praxis.dto;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.math.BigDecimal;

public record PraxisNotificationRequest(
        @JsonProperty("merchant_id") String merchantId,
        @JsonProperty("application_key") String applicationKey,
        Long timestamp,
        Customer customer,
        Session session,
        Transaction transaction
) {

    public record Customer(
            @JsonProperty("customer_token") String customerToken
    ) {}

    public record Session(
            String cid,
            @JsonProperty("order_id") String orderId
    ) {}

    public record Transaction(
            String tid,
            String type,
            String status,
            String currency,
            Long amount,
            @JsonProperty("conversion_rate") BigDecimal conversionRate,
            @JsonProperty("processed_currency") String processedCurrency,
            @JsonProperty("processed_amount") Long processedAmount,
            @JsonProperty("payment_method") String paymentMethod,
            @JsonProperty("payment_processor") String paymentProcessor,
            @JsonProperty("reference_id") String referenceId,
            @JsonProperty("error_code") String errorCode,
            @JsonProperty("error_details") String errorDetails
    ) {}
}

public record PraxisNotificationResponse(
        int result,
        String description
) {}
