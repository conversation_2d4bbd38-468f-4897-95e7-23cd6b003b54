package com.skilling.payment.psp.praxis.dto;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.math.BigDecimal;

public record PraxisInitRequest(
        @JsonProperty("merchant_id") String merchantId,
        @JsonProperty("application_key") String applicationKey,
        String intent,
        String currency,
        String cid,
        @JsonProperty("customer_data") CustomerData customerData,
        String locale,
        @JsonProperty("validation_url") String validationUrl,
        @JsonProperty("notification_url") String notificationUrl,
        @JsonProperty("return_url") String returnUrl,
        Long timestamp,
        String version,
        String variable1,
        @JsonProperty("requester_ip") String requesterIp,
        @JsonProperty("order_id") String orderId,
        BigDecimal amount
) {

    public record CustomerData(
            String email,
            @JsonProperty("first_name") String firstName,
            @JsonProperty("last_name") String lastName,
            String phone,
            String country,
            String city,
            String address,
            @Json<PERSON>roperty("postal_code") String postalCode,
            @JsonProperty("date_of_birth") String dateOfBirth
    ) {}
}

public record PraxisInitResponse(
        boolean success,
        @JsonProperty("redirect_url") String redirectUrl,
        String message,
        @JsonProperty("error_code") String errorCode
) {}
