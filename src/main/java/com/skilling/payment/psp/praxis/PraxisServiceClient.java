package com.skilling.payment.psp.praxis;

import com.skilling.payment.config.PaymentProperties;
import com.skilling.payment.psp.praxis.dto.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

@Service
public class PraxisServiceClient {

    private static final Logger logger = LoggerFactory.getLogger(PraxisServiceClient.class);

    private final WebClient webClient;
    private final PaymentProperties paymentProperties;
    private final PraxisSignatureService signatureService;

    public PraxisServiceClient(@Qualifier("praxisWebClient") WebClient webClient,
                              PaymentProperties paymentProperties,
                              PraxisSignatureService signatureService) {
        this.webClient = webClient;
        this.paymentProperties = paymentProperties;
        this.signatureService = signatureService;
    }

    public Mono<PraxisInitResponse> initializePayment(PraxisInitRequest request, String license) {
        logger.info("Initializing Praxis payment for intent: {}", request.intent());
        
        PaymentProperties.PraxisLicenseProperties licenseConfig = getLicenseConfig(license);
        String signature = signatureService.generateInitSignature(request, licenseConfig);
        
        return webClient
                .post()
                .uri("/cashier/cashier")
                .header(paymentProperties.praxis().authHeader(), signature)
                .header(HttpHeaders.CONTENT_TYPE, "application/json; charset=utf-8")
                .bodyValue(request)
                .retrieve()
                .bodyToMono(PraxisInitResponse.class)
                .doOnSuccess(response -> logger.info("Successfully initialized Praxis payment"))
                .doOnError(error -> logger.error("Error initializing Praxis payment", error));
    }

    public Mono<PraxisFindTransactionResponse> findTransaction(String transactionId, String license) {
        logger.info("Finding Praxis transaction: {}", transactionId);
        
        PaymentProperties.PraxisLicenseProperties licenseConfig = getLicenseConfig(license);
        PraxisFindTransactionRequest request = new PraxisFindTransactionRequest(
                licenseConfig.merchantId(),
                licenseConfig.applicationKey(),
                transactionId,
                "1.3",
                System.currentTimeMillis() / 1000
        );
        
        String signature = signatureService.generateFindTransactionSignature(request, licenseConfig);
        
        return webClient
                .post()
                .uri("/agent/find-transaction")
                .header(paymentProperties.praxis().authHeader(), signature)
                .header(HttpHeaders.CONTENT_TYPE, "application/json; charset=utf-8")
                .bodyValue(request)
                .retrieve()
                .bodyToMono(PraxisFindTransactionResponse.class)
                .doOnSuccess(response -> logger.info("Successfully found Praxis transaction: {}", transactionId))
                .doOnError(error -> logger.error("Error finding Praxis transaction: {}", transactionId, error));
    }

    public Mono<PraxisManageWithdrawalResponse> manageWithdrawalRequest(PraxisManageWithdrawalRequest request, String license) {
        logger.info("Managing Praxis withdrawal request for TID: {}", request.tid());
        
        PaymentProperties.PraxisLicenseProperties licenseConfig = getLicenseConfig(license);
        String signature = signatureService.generateManageWithdrawalSignature(request, licenseConfig);
        
        return webClient
                .post()
                .uri("/agent/manage-withdrawal-request")
                .header(paymentProperties.praxis().authHeader(), signature)
                .header(HttpHeaders.CONTENT_TYPE, "application/json; charset=utf-8")
                .bodyValue(request)
                .retrieve()
                .bodyToMono(PraxisManageWithdrawalResponse.class)
                .doOnSuccess(response -> logger.info("Successfully managed withdrawal request for TID: {}", request.tid()))
                .doOnError(error -> logger.error("Error managing withdrawal request for TID: {}", request.tid(), error));
    }

    private PaymentProperties.PraxisLicenseProperties getLicenseConfig(String license) {
        return switch (license.toUpperCase()) {
            case "SEYCHELLES", "FSA" -> paymentProperties.praxis().fsa();
            case "CYSEC" -> paymentProperties.praxis().cysec();
            default -> throw new IllegalArgumentException("Unknown license: " + license);
        };
    }
}
