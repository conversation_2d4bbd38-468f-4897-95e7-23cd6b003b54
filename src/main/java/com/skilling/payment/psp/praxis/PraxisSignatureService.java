package com.skilling.payment.psp.praxis;

import com.skilling.payment.config.PaymentProperties;
import com.skilling.payment.psp.praxis.dto.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.HexFormat;
import java.util.List;

@Service
public class PraxisSignatureService {

    private static final Logger logger = LoggerFactory.getLogger(PraxisSignatureService.class);
    private static final String HMAC_SHA256 = "HmacSHA256";

    public String generateInitSignature(PraxisInitRequest request, PaymentProperties.PraxisLicenseProperties licenseConfig) {
        List<String> fields = List.of(
                request.merchantId(),
                request.applicationKey(),
                request.intent(),
                request.currency(),
                request.cid(),
                request.timestamp().toString(),
                request.version()
        );
        
        return generateSignature(fields, licenseConfig.merchantSecret());
    }

    public String generateNotificationSignature(PraxisNotificationRequest request, PaymentProperties.PraxisLicenseProperties licenseConfig) {
        List<String> fields = List.of(
                request.merchantId(),
                request.applicationKey(),
                request.timestamp().toString(),
                request.customer().customerToken(),
                request.session().orderId(),
                request.transaction().tid(),
                request.transaction().currency(),
                request.transaction().amount().toString(),
                request.transaction().conversionRate().toString(),
                request.transaction().processedCurrency(),
                request.transaction().processedAmount().toString()
        );
        
        return generateSignature(fields, licenseConfig.merchantSecret());
    }

    public String generateFindTransactionSignature(PraxisFindTransactionRequest request, PaymentProperties.PraxisLicenseProperties licenseConfig) {
        List<String> fields = List.of(
                request.merchantId(),
                request.applicationKey(),
                request.tid(),
                request.timestamp().toString(),
                request.version()
        );
        
        return generateSignature(fields, licenseConfig.merchantSecret());
    }

    public String generateManageWithdrawalSignature(PraxisManageWithdrawalRequest request, PaymentProperties.PraxisLicenseProperties licenseConfig) {
        List<String> fields = List.of(
                request.merchantId(),
                request.applicationKey(),
                request.tid(),
                request.action(),
                request.timestamp().toString(),
                request.version()
        );
        
        return generateSignature(fields, licenseConfig.merchantSecret());
    }

    public boolean validateSignature(List<String> fields, String receivedSignature, PaymentProperties.PraxisLicenseProperties licenseConfig) {
        String expectedSignature = generateSignature(fields, licenseConfig.merchantSecret());
        return expectedSignature.equals(receivedSignature);
    }

    private String generateSignature(List<String> fields, String secret) {
        try {
            String data = String.join("", fields);
            logger.debug("Generating signature for data: {}", data);
            
            Mac mac = Mac.getInstance(HMAC_SHA256);
            SecretKeySpec secretKeySpec = new SecretKeySpec(secret.getBytes(StandardCharsets.UTF_8), HMAC_SHA256);
            mac.init(secretKeySpec);
            
            byte[] hash = mac.doFinal(data.getBytes(StandardCharsets.UTF_8));
            String signature = HexFormat.of().formatHex(hash);
            
            logger.debug("Generated signature: {}", signature);
            return signature;
        } catch (NoSuchAlgorithmException | InvalidKeyException e) {
            logger.error("Error generating signature", e);
            throw new RuntimeException("Failed to generate signature", e);
        }
    }
}
