package com.skilling.payment.psp.praxis.dto;

import com.fasterxml.jackson.annotation.JsonProperty;

public record PraxisFindTransactionRequest(
        @JsonProperty("merchant_id") String merchantId,
        @JsonProperty("application_key") String applicationKey,
        String tid,
        String version,
        Long timestamp
) {}

public record PraxisFindTransactionResponse(
        boolean success,
        Transaction transaction,
        String message
) {
    public record Transaction(
            String tid,
            String type,
            String status,
            String currency,
            Long amount,
            @JsonProperty("payment_method") String paymentMethod,
            @JsonProperty("payment_processor") String paymentProcessor
    ) {}
}

public record PraxisManageWithdrawalRequest(
        @JsonProperty("merchant_id") String merchantId,
        @JsonProperty("application_key") String applicationKey,
        String tid,
        String action,
        Long timestamp,
        String version
) {}

public record PraxisManageWithdrawalResponse(
        boolean success,
        String message,
        String status
) {}
