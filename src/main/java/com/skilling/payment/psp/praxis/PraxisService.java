package com.skilling.payment.psp.praxis;

import com.skilling.payment.config.PaymentProperties;
import com.skilling.payment.controller.PraxisController.*;
import com.skilling.payment.domain.Transaction;
import com.skilling.payment.domain.enums.DestinationStatus;
import com.skilling.payment.domain.enums.DestinationType;
import com.skilling.payment.domain.enums.TransactionStatus;
import com.skilling.payment.domain.enums.TransactionType;
import com.skilling.payment.external.CustomerServiceClient;
import com.skilling.payment.external.dto.CustomerDetails;
import com.skilling.payment.psp.praxis.dto.*;
import com.skilling.payment.service.AccountMappingService;
import com.skilling.payment.service.TransactionService;
import jakarta.servlet.http.HttpServletRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.Map;
import java.util.UUID;

@Service
@Transactional
public class PraxisService {

    private static final Logger logger = LoggerFactory.getLogger(PraxisService.class);

    private final PraxisServiceClient praxisClient;
    private final PraxisSignatureService signatureService;
    private final TransactionService transactionService;
    private final AccountMappingService accountMappingService;
    private final CustomerServiceClient customerServiceClient;
    private final PaymentProperties paymentProperties;

    public PraxisService(PraxisServiceClient praxisClient,
                        PraxisSignatureService signatureService,
                        TransactionService transactionService,
                        AccountMappingService accountMappingService,
                        CustomerServiceClient customerServiceClient,
                        PaymentProperties paymentProperties) {
        this.praxisClient = praxisClient;
        this.signatureService = signatureService;
        this.transactionService = transactionService;
        this.accountMappingService = accountMappingService;
        this.customerServiceClient = customerServiceClient;
        this.paymentProperties = paymentProperties;
    }

    public Map<String, Object> initializePayment(Long customerId, String accountId, 
                                               InitializePaymentRequest request, String intent, 
                                               String license, HttpServletRequest httpRequest) {
        logger.info("Initializing Praxis {} for customerId: {}, accountId: {}", intent, customerId, accountId);
        
        try {
            // Get customer details
            CustomerDetails customer = customerServiceClient
                    .getCustomerDetails(customerId, getRequestHeaders(httpRequest))
                    .block();
            
            if (customer == null) {
                return createErrorResponse("CUSTOMER_NOT_FOUND", "Customer not found");
            }

            // Get account mapping
            var accountMapping = accountMappingService.findByCustomerId(customerId);
            if (accountMapping.isEmpty()) {
                return createErrorResponse("ACCOUNT_MAPPING_NOT_FOUND", "Account mapping not found");
            }

            // Build Praxis request
            PaymentProperties.PraxisLicenseProperties licenseConfig = getLicenseConfig(license);
            PraxisInitRequest praxisRequest = buildPraxisInitRequest(
                    customer, accountMapping.get(), request, intent, licenseConfig, httpRequest);

            // Call Praxis API
            PraxisInitResponse praxisResponse = praxisClient
                    .initializePayment(praxisRequest, license)
                    .block();

            if (praxisResponse != null && praxisResponse.success()) {
                return Map.of(
                        "success", true,
                        "paymentUrl", praxisResponse.redirectUrl()
                );
            } else {
                return createErrorResponse("PRAXIS_ERROR", 
                        praxisResponse != null ? praxisResponse.message() : "Unknown error");
            }
        } catch (Exception e) {
            logger.error("Error initializing Praxis payment", e);
            return createErrorResponse("INTERNAL_ERROR", "Internal server error");
        }
    }

    public PraxisNotificationResponse handleNotification(PraxisNotificationRequest request, 
                                                       String license, HttpServletRequest httpRequest) {
        logger.info("Handling Praxis notification for TID: {}", request.transaction().tid());
        
        try {
            // Validate signature
            PaymentProperties.PraxisLicenseProperties licenseConfig = getLicenseConfig(license);
            String receivedSignature = httpRequest.getHeader(paymentProperties.praxis().authHeader());
            
            if (!signatureService.validateSignature(
                    buildNotificationSignatureFields(request), receivedSignature, licenseConfig)) {
                logger.warn("Invalid signature for Praxis notification");
                return new PraxisNotificationResponse(1, "Invalid signature");
            }

            // Process transaction
            processNotificationTransaction(request, license);
            
            return new PraxisNotificationResponse(0, "Ok");
        } catch (Exception e) {
            logger.error("Error handling Praxis notification", e);
            return new PraxisNotificationResponse(-1, "Internal error");
        }
    }

    public Map<String, Object> handleValidation(Map<String, Object> request, 
                                               String license, HttpServletRequest httpRequest) {
        logger.info("Handling Praxis validation request");
        
        try {
            // Extract customer ID from request
            String customerId = (String) request.get("customer_id");
            if (customerId != null) {
                // Validate customer exists
                CustomerDetails customer = customerServiceClient
                        .getCustomerDetails(Long.parseLong(customerId), getRequestHeaders(httpRequest))
                        .block();
                
                if (customer != null) {
                    return Map.of(
                            "result", 0,
                            "description", "Ok"
                    );
                }
            }
            
            return Map.of(
                    "result", 1,
                    "description", "Customer not found"
            );
        } catch (Exception e) {
            logger.error("Error handling Praxis validation", e);
            return Map.of(
                    "result", -1,
                    "description", "Internal error"
            );
        }
    }

    public Map<String, Object> handleCustomerSync(CustomerSyncRequest request, HttpServletRequest httpRequest) {
        logger.info("Handling Praxis customer sync for customerId: {}", request.customerId());
        
        try {
            // Create or update account mapping if needed
            Long customerId = Long.parseLong(request.customerId());
            accountMappingService.findByCustomerId(customerId)
                    .orElseGet(() -> {
                        // Create new account mapping
                        return accountMappingService.createAccountMapping(customerId, null, null);
                    });
            
            return Map.of(
                    "success", true,
                    "customerId", request.customerId()
            );
        } catch (Exception e) {
            logger.error("Error handling customer sync", e);
            return Map.of(
                    "success", false,
                    "error", "Customer sync failed"
            );
        }
    }

    public Map<String, Object> cancelWithdrawal(String tid, String license, HttpServletRequest httpRequest) {
        logger.info("Cancelling Praxis withdrawal for TID: {}", tid);
        
        try {
            PaymentProperties.PraxisLicenseProperties licenseConfig = getLicenseConfig(license);
            PraxisManageWithdrawalRequest request = new PraxisManageWithdrawalRequest(
                    licenseConfig.merchantId(),
                    licenseConfig.applicationKey(),
                    tid,
                    "cancel",
                    Instant.now().getEpochSecond(),
                    "1.3"
            );
            
            PraxisManageWithdrawalResponse response = praxisClient
                    .manageWithdrawalRequest(request, license)
                    .block();
            
            if (response != null && response.success()) {
                return Map.of(
                        "success", true,
                        "tid", tid,
                        "status", "cancelled"
                );
            } else {
                return createErrorResponse("CANCEL_FAILED", 
                        response != null ? response.message() : "Cancel failed");
            }
        } catch (Exception e) {
            logger.error("Error cancelling withdrawal", e);
            return createErrorResponse("INTERNAL_ERROR", "Internal server error");
        }
    }

    private void processNotificationTransaction(PraxisNotificationRequest request, String license) {
        var tx = request.transaction();
        String externalId = request.session().cid();
        
        // Find or create transaction
        Transaction transaction = transactionService.findByExternalId(externalId)
                .orElseGet(() -> {
                    Transaction newTx = new Transaction();
                    newTx.setExternalId(externalId);
                    newTx.setCustomerId(Long.parseLong(request.customer().customerToken()));
                    newTx.setPsp("PRAXIS");
                    newTx.setDestinationType(DestinationType.PRAXIS);
                    return newTx;
                });
        
        // Update transaction details
        transaction.setAmount(new BigDecimal(tx.amount()).divide(new BigDecimal(100))); // Convert from cents
        transaction.setCurrency(tx.currency());
        transaction.setStatus(mapPraxisStatus(tx.status()));
        transaction.setTransactionType(mapPraxisType(tx.type()));
        transaction.setDestinationStatus(tx.status().equals("approved") ? 
                DestinationStatus.SUCCESSFUL : DestinationStatus.FAILED);
        transaction.setPaymentMethod(tx.paymentMethod());
        transaction.setPaymentProcessor(tx.paymentProcessor());
        transaction.setReference(tx.tid());
        
        transactionService.createTransaction(transaction);
    }

    private TransactionStatus mapPraxisStatus(String praxisStatus) {
        return switch (praxisStatus.toLowerCase()) {
            case "approved" -> TransactionStatus.FINISHED;
            case "declined", "rejected" -> TransactionStatus.REJECTED;
            case "pending" -> TransactionStatus.PENDING;
            case "cancelled" -> TransactionStatus.CANCELED;
            default -> TransactionStatus.PENDING;
        };
    }

    private TransactionType mapPraxisType(String praxisType) {
        return switch (praxisType.toLowerCase()) {
            case "sale" -> TransactionType.DEPOSIT;
            case "payout" -> TransactionType.WD;
            case "refund" -> TransactionType.REFUND;
            default -> TransactionType.DEPOSIT;
        };
    }

    private PraxisInitRequest buildPraxisInitRequest(CustomerDetails customer, 
                                                   var accountMapping, 
                                                   InitializePaymentRequest request, 
                                                   String intent,
                                                   PaymentProperties.PraxisLicenseProperties licenseConfig,
                                                   HttpServletRequest httpRequest) {
        
        String callbackUrlRoot = paymentProperties.praxis().callbackUrlRoot();
        String license = licenseConfig == paymentProperties.praxis().fsa() ? "SEYCHELLES" : "CYSEC";
        
        return new PraxisInitRequest(
                licenseConfig.merchantId(),
                licenseConfig.applicationKey(),
                intent,
                request.currency(),
                accountMapping.getExternalId().toString(),
                new PraxisInitRequest.CustomerData(
                        customer.contactInformation().email(),
                        customer.personalInformation().firstName(),
                        customer.personalInformation().lastName(),
                        customer.contactInformation().phone(),
                        customer.contactInformation().country(),
                        customer.contactInformation().city(),
                        customer.contactInformation().address(),
                        customer.contactInformation().postalCode(),
                        customer.personalInformation().dateOfBirth()
                ),
                request.locale(),
                callbackUrlRoot + "/validation?licence=" + license,
                callbackUrlRoot + "/notification?licence=" + license,
                request.returnUrl(),
                Instant.now().getEpochSecond(),
                "1.3",
                "payment-service-" + UUID.randomUUID(),
                getClientIp(httpRequest),
                UUID.randomUUID().toString(),
                request.amount()
        );
    }

    private PaymentProperties.PraxisLicenseProperties getLicenseConfig(String license) {
        return switch (license.toUpperCase()) {
            case "SEYCHELLES", "FSA" -> paymentProperties.praxis().fsa();
            case "CYSEC" -> paymentProperties.praxis().cysec();
            default -> throw new IllegalArgumentException("Unknown license: " + license);
        };
    }

    private Map<String, Object> createErrorResponse(String errorCode, String message) {
        return Map.of(
                "success", false,
                "errorCode", errorCode,
                "message", message
        );
    }

    private Map<String, String> getRequestHeaders(HttpServletRequest request) {
        return Map.of(
                "x-function", request.getHeader("x-function") != null ? request.getHeader("x-function") : "",
                "x-user", request.getHeader("x-user") != null ? request.getHeader("x-user") : "",
                "x-customer_id", request.getHeader("x-customer_id") != null ? request.getHeader("x-customer_id") : ""
        );
    }

    private String getClientIp(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty()) {
            return xForwardedFor.split(",")[0].trim();
        }
        return request.getRemoteAddr();
    }

    private java.util.List<String> buildNotificationSignatureFields(PraxisNotificationRequest request) {
        return java.util.List.of(
                request.merchantId(),
                request.applicationKey(),
                request.timestamp().toString(),
                request.customer().customerToken(),
                request.session().orderId(),
                request.transaction().tid(),
                request.transaction().currency(),
                request.transaction().amount().toString(),
                request.transaction().conversionRate().toString(),
                request.transaction().processedCurrency(),
                request.transaction().processedAmount().toString()
        );
    }
}
