package com.skilling.payment.psp.devcode.dto;

import java.math.BigDecimal;

public record DevcodeWithdrawalRequest(
        String sessionId,
        String paymentAccountId,
        String merchantId,
        BigDecimal amount,
        String currency,
        String paymentMethod,
        WithdrawalDetails details
) {

    public record WithdrawalDetails(
            String accountNumber,
            String routingNumber,
            String bankName,
            String accountHolderName,
            String email
    ) {}
}

public record DevcodeWithdrawalResponse(
        boolean success,
        String transactionId,
        String status,
        String message,
        String redirectUrl
) {}
