package com.skilling.payment.psp.devcode;

import com.skilling.payment.config.PaymentProperties;
import com.skilling.payment.controller.DevcodeController.*;
import com.skilling.payment.domain.Transaction;
import com.skilling.payment.domain.enums.TransactionStatus;
import com.skilling.payment.domain.enums.TransactionType;
import com.skilling.payment.service.TransactionService;
import com.skilling.payment.service.AccountMappingService;
import com.skilling.payment.external.CustomerServiceClient;
import jakarta.servlet.http.HttpServletRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Map;
import java.util.UUID;

@Service
@Transactional
public class DevcodeService {

    private static final Logger logger = LoggerFactory.getLogger(DevcodeService.class);

    private final DevcodeServiceClient devcodeClient;
    private final TransactionService transactionService;
    private final AccountMappingService accountMappingService;
    private final CustomerServiceClient customerServiceClient;
    private final PaymentProperties paymentProperties;

    public DevcodeService(DevcodeServiceClient devcodeClient,
                         TransactionService transactionService,
                         AccountMappingService accountMappingService,
                         CustomerServiceClient customerServiceClient,
                         PaymentProperties paymentProperties) {
        this.devcodeClient = devcodeClient;
        this.transactionService = transactionService;
        this.accountMappingService = accountMappingService;
        this.customerServiceClient = customerServiceClient;
        this.paymentProperties = paymentProperties;
    }

    public Map<String, Object> createPaymentToken(Long customerId, CreateTokenRequest request, 
                                                 String tokenType, HttpServletRequest httpRequest) {
        logger.info("Creating {} token for customerId: {}", tokenType, customerId);
        
        try {
            // Generate session ID and token
            String sessionId = UUID.randomUUID().toString();
            String token = generateJwtToken(customerId, request, tokenType, sessionId);
            
            return Map.of(
                    "success", true,
                    "sessionId", sessionId,
                    "token", token,
                    "redirectUrl", buildRedirectUrl(tokenType, sessionId)
            );
        } catch (Exception e) {
            logger.error("Error creating payment token for customerId: {}", customerId, e);
            return Map.of(
                    "success", false,
                    "error", "Failed to create payment token"
            );
        }
    }

    public Map<String, Object> verifyUser(VerifyUserRequest request, HttpServletRequest httpRequest) {
        logger.info("Verifying user for sessionId: {}, userId: {}", request.sessionId(), request.userId());
        
        try {
            // Validate user exists and is active
            Long customerId = Long.parseLong(request.userId());
            
            // Get customer details to verify
            customerServiceClient.getCustomerDetails(customerId, getRequestHeaders(httpRequest))
                    .block(); // For simplicity, using blocking call
            
            return Map.of(
                    "success", true,
                    "userId", request.userId(),
                    "sessionId", request.sessionId(),
                    "balance", "1000.00", // Mock balance
                    "currency", "EUR"
            );
        } catch (Exception e) {
            logger.error("Error verifying user for sessionId: {}", request.sessionId(), e);
            return Map.of(
                    "success", false,
                    "errCode", "USER_NOT_FOUND",
                    "errMsg", "User verification failed"
            );
        }
    }

    public Map<String, Object> authorize(AuthorizeRequest request, HttpServletRequest httpRequest) {
        logger.info("Authorizing transaction for sessionId: {}, amount: {} {}", 
                   request.sessionId(), request.amount(), request.currency());
        
        try {
            Long customerId = Long.parseLong(request.userId());
            BigDecimal amount = new BigDecimal(request.amount());
            
            // Create authorization record
            Transaction transaction = new Transaction();
            transaction.setCustomerId(customerId);
            transaction.setAmount(amount);
            transaction.setCurrency(request.currency());
            transaction.setStatus(TransactionStatus.REQUESTED);
            transaction.setTransactionType(TransactionType.DEPOSIT);
            transaction.setExternalId(request.sessionId());
            transaction.setPsp("DEVCODE");
            
            transactionService.createTransaction(transaction);
            
            return Map.of(
                    "success", true,
                    "txId", transaction.getId().toString(),
                    "sessionId", request.sessionId(),
                    "authCode", UUID.randomUUID().toString()
            );
        } catch (Exception e) {
            logger.error("Error authorizing transaction for sessionId: {}", request.sessionId(), e);
            return Map.of(
                    "success", false,
                    "errCode", "AUTHORIZATION_FAILED",
                    "errMsg", "Transaction authorization failed"
            );
        }
    }

    public Map<String, Object> transfer(TransferRequest request, HttpServletRequest httpRequest) {
        logger.info("Processing transfer for sessionId: {}, transactionId: {}", 
                   request.sessionId(), request.transactionId());
        
        try {
            Long customerId = Long.parseLong(request.userId());
            BigDecimal amount = new BigDecimal(request.amount());
            
            // Find existing transaction or create new one
            Transaction transaction = transactionService.findByExternalId(request.sessionId())
                    .orElseGet(() -> {
                        Transaction newTx = new Transaction();
                        newTx.setCustomerId(customerId);
                        newTx.setAmount(amount);
                        newTx.setCurrency(request.currency());
                        newTx.setExternalId(request.sessionId());
                        newTx.setPsp("DEVCODE");
                        newTx.setTransactionType(TransactionType.DEPOSIT);
                        return newTx;
                    });
            
            transaction.setStatus(TransactionStatus.FINISHED);
            transactionService.createTransaction(transaction);
            
            return Map.of(
                    "success", true,
                    "txId", transaction.getId().toString(),
                    "sessionId", request.sessionId(),
                    "balance", "1000.00" // Mock updated balance
            );
        } catch (Exception e) {
            logger.error("Error processing transfer for sessionId: {}", request.sessionId(), e);
            return Map.of(
                    "success", false,
                    "errCode", "TRANSFER_FAILED",
                    "errMsg", "Transfer processing failed"
            );
        }
    }

    public Map<String, Object> cancel(CancelRequest request, HttpServletRequest httpRequest) {
        logger.info("Cancelling transaction for sessionId: {}, transactionId: {}", 
                   request.sessionId(), request.transactionId());
        
        try {
            // Find and cancel transaction
            transactionService.findByExternalId(request.sessionId())
                    .ifPresent(transaction -> {
                        transaction.setStatus(TransactionStatus.CANCELED);
                        transactionService.createTransaction(transaction);
                    });
            
            return Map.of(
                    "success", true,
                    "sessionId", request.sessionId(),
                    "status", "CANCELLED"
            );
        } catch (Exception e) {
            logger.error("Error cancelling transaction for sessionId: {}", request.sessionId(), e);
            return Map.of(
                    "success", false,
                    "errCode", "CANCEL_FAILED",
                    "errMsg", "Transaction cancellation failed"
            );
        }
    }

    private String generateJwtToken(Long customerId, CreateTokenRequest request, String tokenType, String sessionId) {
        // TODO: Implement JWT token generation using the configured signing key
        return "jwt_token_" + sessionId;
    }

    private String buildRedirectUrl(String tokenType, String sessionId) {
        String baseUrl = paymentProperties.externalServices().devcodeService().baseUrl();
        return baseUrl + "/cashier?sessionId=" + sessionId + "&type=" + tokenType;
    }

    private Map<String, String> getRequestHeaders(HttpServletRequest request) {
        return Map.of(
                "x-function", request.getHeader("x-function") != null ? request.getHeader("x-function") : "",
                "x-user", request.getHeader("x-user") != null ? request.getHeader("x-user") : "",
                "x-customer_id", request.getHeader("x-customer_id") != null ? request.getHeader("x-customer_id") : ""
        );
    }
}
