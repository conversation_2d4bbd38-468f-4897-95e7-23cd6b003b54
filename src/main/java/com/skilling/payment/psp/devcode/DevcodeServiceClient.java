package com.skilling.payment.psp.devcode;

import com.skilling.payment.psp.devcode.dto.DevcodePaymentMethodsResponse;
import com.skilling.payment.psp.devcode.dto.DevcodeWithdrawalRequest;
import com.skilling.payment.psp.devcode.dto.DevcodeWithdrawalResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

import java.util.Map;

@Service
public class DevcodeServiceClient {

    private static final Logger logger = LoggerFactory.getLogger(DevcodeServiceClient.class);

    private final WebClient webClient;

    private static final Map<String, String> WITHDRAWAL_ENDPOINTS = Map.of(
            "creditcard", "creditcard/withdrawal/process",
            "trustly", "trustly/withdrawal/process",
            "skrill", "skrill/withdrawal/process",
            "paypal", "paypal/withdrawal/process",
            "neteller", "neteller/withdrawal/process"
    );

    public DevcodeServiceClient(@Qualifier("devcodeServiceWebClient") WebClient webClient) {
        this.webClient = webClient;
    }

    public Mono<DevcodePaymentMethodsResponse> getCustomerPaymentMethods(String paymentAccountId, String sessionId, 
                                                                        String method, String merchantId) {
        logger.info("Fetching payment methods for paymentAccountId: {}, method: {}", paymentAccountId, method);
        
        return webClient
                .get()
                .uri("/user/payment/method/{merchantId}/{paymentAccountId}?sessionId={sessionId}&method={method}",
                     merchantId, paymentAccountId, sessionId, method)
                .retrieve()
                .bodyToMono(DevcodePaymentMethodsResponse.class)
                .doOnSuccess(response -> logger.info("Successfully fetched payment methods for paymentAccountId: {}", paymentAccountId))
                .doOnError(error -> logger.error("Error fetching payment methods for paymentAccountId: {}", paymentAccountId, error));
    }

    public Mono<DevcodeWithdrawalResponse> processWithdrawal(String paymentMethod, DevcodeWithdrawalRequest request) {
        String endpoint = WITHDRAWAL_ENDPOINTS.get(paymentMethod.toLowerCase());
        if (endpoint == null) {
            return Mono.error(new IllegalArgumentException("Unsupported payment method: " + paymentMethod));
        }

        logger.info("Processing withdrawal for payment method: {}", paymentMethod);
        
        return webClient
                .post()
                .uri("/{endpoint}", endpoint)
                .bodyValue(request)
                .retrieve()
                .bodyToMono(DevcodeWithdrawalResponse.class)
                .doOnSuccess(response -> logger.info("Successfully processed withdrawal for payment method: {}", paymentMethod))
                .doOnError(error -> logger.error("Error processing withdrawal for payment method: {}", paymentMethod, error));
    }
}
