package com.skilling.payment.controller;

import com.skilling.payment.psp.praxis.PraxisService;
import com.skilling.payment.psp.praxis.dto.PraxisNotificationRequest;
import com.skilling.payment.psp.praxis.dto.PraxisNotificationResponse;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.Map;

@RestController
@RequestMapping("/payment/praxis")
public class PraxisController {

    private final PraxisService praxisService;

    public PraxisController(PraxisService praxisService) {
        this.praxisService = praxisService;
    }

    @PostMapping("/customer/{customerId}/account/{accountId}/deposit")
    public ResponseEntity<Map<String, Object>> initializeDeposit(
            @PathVariable Long customerId,
            @PathVariable String accountId,
            @Valid @RequestBody InitializePaymentRequest request,
            @RequestParam(required = false, defaultValue = "SEYCHELLES") String license,
            HttpServletRequest httpRequest) {
        
        Map<String, Object> response = praxisService.initializePayment(
                customerId, accountId, request, "payment", license, httpRequest);
        return ResponseEntity.ok(response);
    }

    @PostMapping("/customer/{customerId}/account/{accountId}/withdrawal")
    public ResponseEntity<Map<String, Object>> initializeWithdrawal(
            @PathVariable Long customerId,
            @PathVariable String accountId,
            @Valid @RequestBody InitializePaymentRequest request,
            @RequestParam(required = false, defaultValue = "SEYCHELLES") String license,
            HttpServletRequest httpRequest) {
        
        Map<String, Object> response = praxisService.initializePayment(
                customerId, accountId, request, "withdrawal", license, httpRequest);
        return ResponseEntity.ok(response);
    }

    @PostMapping("/validation")
    public ResponseEntity<Map<String, Object>> validation(
            @Valid @RequestBody Map<String, Object> request,
            @RequestParam(required = false, defaultValue = "SEYCHELLES") String license,
            HttpServletRequest httpRequest) {
        
        Map<String, Object> response = praxisService.handleValidation(request, license, httpRequest);
        return ResponseEntity.ok(response);
    }

    @PostMapping("/notification")
    public ResponseEntity<PraxisNotificationResponse> notification(
            @Valid @RequestBody PraxisNotificationRequest request,
            @RequestParam(required = false, defaultValue = "SEYCHELLES") String license,
            HttpServletRequest httpRequest) {
        
        PraxisNotificationResponse response = praxisService.handleNotification(request, license, httpRequest);
        return ResponseEntity.ok(response);
    }

    @PostMapping("/sync")
    public ResponseEntity<Map<String, Object>> customerSync(
            @Valid @RequestBody CustomerSyncRequest request,
            HttpServletRequest httpRequest) {
        
        Map<String, Object> response = praxisService.handleCustomerSync(request, httpRequest);
        return ResponseEntity.ok(response);
    }

    @PostMapping("/cancel-withdrawal/{tid}")
    public ResponseEntity<Map<String, Object>> cancelWithdrawal(
            @PathVariable String tid,
            @RequestParam(required = false, defaultValue = "SEYCHELLES") String license,
            HttpServletRequest httpRequest) {
        
        Map<String, Object> response = praxisService.cancelWithdrawal(tid, license, httpRequest);
        return ResponseEntity.ok(response);
    }

    // DTOs
    public record InitializePaymentRequest(
            BigDecimal amount,
            String currency,
            String locale,
            String returnUrl
    ) {}

    public record CustomerSyncRequest(
            String customerId,
            String email,
            String firstName,
            String lastName,
            String phone,
            String country
    ) {}
}
