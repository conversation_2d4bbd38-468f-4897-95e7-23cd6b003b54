package com.skilling.payment.controller;

import com.skilling.payment.domain.Transaction;
import com.skilling.payment.service.TransactionService;
import jakarta.validation.Valid;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/payment")
public class TransactionController {

    private final TransactionService transactionService;

    public TransactionController(TransactionService transactionService) {
        this.transactionService = transactionService;
    }

    @GetMapping("/bo/transactions/firstDepositors")
    public ResponseEntity<List<Long>> getFirstDepositors() {
        List<Long> firstDepositors = transactionService.getFirstDepositors();
        return ResponseEntity.ok(firstDepositors);
    }

    @GetMapping("/bo/transactions/{id}")
    public ResponseEntity<Transaction> getTransactionById(@PathVariable Long id) {
        return transactionService.getTransactionById(id)
                .map(ResponseEntity::ok)
                .orElse(ResponseEntity.notFound().build());
    }

    @GetMapping("/bo/customer/{customerId}/transactions/{id}")
    public ResponseEntity<Transaction> getCustomerTransactionById(
            @PathVariable Long customerId,
            @PathVariable Long id) {
        return transactionService.getCustomerTransactionById(customerId, id)
                .map(ResponseEntity::ok)
                .orElse(ResponseEntity.notFound().build());
    }

    @GetMapping("/bo/customer/{customerId}/transactions/reference/{reference}")
    public ResponseEntity<Transaction> getCustomerTransactionByReference(
            @PathVariable Long customerId,
            @PathVariable String reference) {
        return transactionService.getCustomerTransactionByReference(customerId, reference)
                .map(ResponseEntity::ok)
                .orElse(ResponseEntity.notFound().build());
    }

    @GetMapping("/bo/transactions")
    public ResponseEntity<Page<Transaction>> searchTransactions(
            @RequestParam(required = false) Long customerId,
            @RequestParam(required = false) String status,
            @RequestParam(required = false) String transactionType,
            @RequestParam(required = false) String license,
            Pageable pageable) {
        Page<Transaction> transactions = transactionService.searchTransactions(
                customerId, status, transactionType, license, pageable);
        return ResponseEntity.ok(transactions);
    }

    @PostMapping("/manual/customer/{customerId}/account/{accountId}/transaction")
    public ResponseEntity<Transaction> createManualTransaction(
            @PathVariable Long customerId,
            @PathVariable String accountId,
            @Valid @RequestBody CreateManualTransactionRequest request) {
        Transaction transaction = transactionService.createManualTransaction(customerId, accountId, request);
        return ResponseEntity.status(HttpStatus.CREATED).body(transaction);
    }

    @PatchMapping("/bo/manual/customer/{customerId}/transaction/{id}")
    public ResponseEntity<Transaction> patchManualTransaction(
            @PathVariable Long customerId,
            @PathVariable Long id,
            @Valid @RequestBody PatchManualTransactionRequest request) {
        return transactionService.patchManualTransaction(customerId, id, request)
                .map(ResponseEntity::ok)
                .orElse(ResponseEntity.notFound().build());
    }

    // DTOs for request bodies
    public record CreateManualTransactionRequest(
            String transactionType,
            String amount,
            String currency,
            String comment,
            String reference
    ) {}

    public record PatchManualTransactionRequest(
            String status,
            String comment
    ) {}
}
