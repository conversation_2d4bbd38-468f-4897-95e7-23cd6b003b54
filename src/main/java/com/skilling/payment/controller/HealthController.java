package com.skilling.payment.controller;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.util.Map;

@RestController
public class HealthController {

    @Value("${spring.application.name:payment-service}")
    private String applicationName;

    @Value("${payment.environment:TEST}")
    private String environment;

    @GetMapping("/")
    public ResponseEntity<Map<String, Object>> health() {
        return ResponseEntity.ok(Map.of(
                "service", applicationName,
                "status", "UP",
                "environment", environment,
                "timestamp", LocalDateTime.now()
        ));
    }
}
