package com.skilling.payment.controller;

import com.skilling.payment.domain.PaymentMethod;
import com.skilling.payment.service.PaymentMethodService;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/payment")
public class PaymentMethodController {

    private final PaymentMethodService paymentMethodService;

    public PaymentMethodController(PaymentMethodService paymentMethodService) {
        this.paymentMethodService = paymentMethodService;
    }

    @GetMapping("/methods")
    public ResponseEntity<List<PaymentMethod>> getAllMethods() {
        List<PaymentMethod> methods = paymentMethodService.getAllEnabledMethods();
        return ResponseEntity.ok(methods);
    }

    @GetMapping("/methods/firstDeposit")
    public ResponseEntity<List<PaymentMethod>> getMethodsForFirstDeposit() {
        List<PaymentMethod> methods = paymentMethodService.getMethodsForFirstDeposit();
        return ResponseEntity.ok(methods);
    }

    @GetMapping("/methods/v2")
    public ResponseEntity<Map<String, Object>> getAllMethodsV2(
            @RequestParam(required = false) String country,
            @RequestParam(required = false) String currency) {
        
        Map<String, Object> response = paymentMethodService.getAllMethodsV2(country, currency);
        return ResponseEntity.ok(response);
    }

    @GetMapping("/methods/v2/firstDeposit")
    public ResponseEntity<Map<String, Object>> getAllMethodsForFirstDepositV2(
            @RequestParam(required = false) String country,
            @RequestParam(required = false) String currency) {
        
        Map<String, Object> response = paymentMethodService.getMethodsForFirstDepositV2(country, currency);
        return ResponseEntity.ok(response);
    }
}
