package com.skilling.payment.controller;

import com.skilling.payment.psp.devcode.DevcodeService;
import com.skilling.payment.psp.devcode.dto.*;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

@RestController
@RequestMapping("/payment")
public class DevcodeController {

    private final DevcodeService devcodeService;

    public DevcodeController(DevcodeService devcodeService) {
        this.devcodeService = devcodeService;
    }

    @PostMapping("/deposit-token/{customerId}")
    public ResponseEntity<Map<String, Object>> createDepositToken(
            @PathVariable Long customerId,
            @Valid @RequestBody CreateTokenRequest request,
            HttpServletRequest httpRequest) {
        
        Map<String, Object> response = devcodeService.createPaymentToken(customerId, request, "deposit", httpRequest);
        return ResponseEntity.ok(response);
    }

    @PostMapping("/withdrawal-token/{customerId}")
    public ResponseEntity<Map<String, Object>> createWithdrawalToken(
            @PathVariable Long customerId,
            @Valid @RequestBody CreateTokenRequest request,
            HttpServletRequest httpRequest) {
        
        Map<String, Object> response = devcodeService.createPaymentToken(customerId, request, "withdrawal", httpRequest);
        return ResponseEntity.ok(response);
    }

    @PostMapping("/api/v2/verifyuser")
    public ResponseEntity<Map<String, Object>> verifyUser(
            @Valid @RequestBody VerifyUserRequest request,
            HttpServletRequest httpRequest) {
        
        Map<String, Object> response = devcodeService.verifyUser(request, httpRequest);
        return ResponseEntity.ok(response);
    }

    @PostMapping("/api/v2/authorize")
    public ResponseEntity<Map<String, Object>> authorize(
            @Valid @RequestBody AuthorizeRequest request,
            HttpServletRequest httpRequest) {
        
        Map<String, Object> response = devcodeService.authorize(request, httpRequest);
        return ResponseEntity.ok(response);
    }

    @PostMapping("/api/v2/transfer")
    public ResponseEntity<Map<String, Object>> transfer(
            @Valid @RequestBody TransferRequest request,
            HttpServletRequest httpRequest) {
        
        Map<String, Object> response = devcodeService.transfer(request, httpRequest);
        return ResponseEntity.ok(response);
    }

    @PostMapping("/api/v2/cancel")
    public ResponseEntity<Map<String, Object>> cancel(
            @Valid @RequestBody CancelRequest request,
            HttpServletRequest httpRequest) {
        
        Map<String, Object> response = devcodeService.cancel(request, httpRequest);
        return ResponseEntity.ok(response);
    }

    // DTOs
    public record CreateTokenRequest(
            String amount,
            String currency,
            String paymentMethod,
            String returnUrl,
            String locale
    ) {}

    public record VerifyUserRequest(
            String sessionId,
            String userId,
            String merchantId
    ) {}

    public record AuthorizeRequest(
            String sessionId,
            String userId,
            String merchantId,
            String amount,
            String currency
    ) {}

    public record TransferRequest(
            String sessionId,
            String userId,
            String merchantId,
            String amount,
            String currency,
            String transactionId
    ) {}

    public record CancelRequest(
            String sessionId,
            String userId,
            String merchantId,
            String transactionId
    ) {}
}
