package com.skilling.payment.external;

import com.skilling.payment.external.dto.AccountBalance;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

import java.math.BigDecimal;
import java.util.Map;

@Service
public class TradingBalanceServiceClient {

    private static final Logger logger = LoggerFactory.getLogger(TradingBalanceServiceClient.class);

    private final WebClient webClient;

    public TradingBalanceServiceClient(@Qualifier("tradingBalanceServiceWebClient") WebClient webClient) {
        this.webClient = webClient;
    }

    public Mono<AccountBalance> getAccountBalance(String tradingAccountId, Long customerId, Map<String, String> headers) {
        logger.info("Fetching account balance for customerId: {}, tradingAccountId: {}", customerId, tradingAccountId);
        
        return webClient
                .get()
                .uri("/v1/customers/{customerId}/accounts/{tradingAccountId}/balance", customerId, tradingAccountId)
                .headers(httpHeaders -> {
                    headers.forEach(httpHeaders::set);
                    httpHeaders.set("x-sub_caller", "payment-service");
                })
                .retrieve()
                .bodyToMono(AccountBalance.class)
                .doOnSuccess(balance -> logger.info("Successfully fetched account balance for customerId: {}", customerId))
                .doOnError(error -> logger.error("Error fetching account balance for customerId: {}", customerId, error));
    }

    public Mono<BigDecimal> getAccountWithdrawableAmount(String tradingAccountId, Long customerId, Map<String, String> headers) {
        return getAccountBalance(tradingAccountId, customerId, headers)
                .map(balance -> balance.withdrawableAmount() != null ? balance.withdrawableAmount() : BigDecimal.ZERO);
    }
}
