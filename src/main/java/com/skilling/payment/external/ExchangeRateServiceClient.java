package com.skilling.payment.external;

import com.skilling.payment.external.dto.ExchangeRateResponse;
import com.skilling.payment.external.dto.ExchangeAmountInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Map;

@Service
public class ExchangeRateServiceClient {

    private static final Logger logger = LoggerFactory.getLogger(ExchangeRateServiceClient.class);

    private final WebClient webClient;

    public ExchangeRateServiceClient(@Qualifier("exchangeRateServiceWebClient") WebClient webClient) {
        this.webClient = webClient;
    }

    public Mono<ExchangeRateResponse> getLatestRates(String baseCurrency, Map<String, String> headers) {
        logger.info("Fetching exchange rates for base currency: {}", baseCurrency);
        
        return webClient
                .get()
                .uri("/latest?base={baseCurrency}", baseCurrency)
                .headers(httpHeaders -> {
                    headers.forEach(httpHeaders::set);
                    httpHeaders.set("x-sub_caller", "payment-service");
                })
                .retrieve()
                .bodyToMono(ExchangeRateResponse.class)
                .doOnSuccess(rates -> logger.info("Successfully fetched exchange rates for base currency: {}", baseCurrency))
                .doOnError(error -> logger.error("Error fetching exchange rates for base currency: {}", baseCurrency, error));
    }

    public Mono<ExchangeAmountInfo> getEurUsdExchangeAmount(BigDecimal depositAmount, String depositCurrency, Map<String, String> headers) {
        logger.info("Converting amount {} {} to EUR and USD", depositAmount, depositCurrency);
        
        return getLatestRates(depositCurrency, headers)
                .map(response -> {
                    BigDecimal eurRate = response.rates().get("EUR");
                    BigDecimal usdRate = response.rates().get("USD");
                    
                    if (eurRate == null || usdRate == null) {
                        logger.warn("EUR or USD rate not found for currency: {}", depositCurrency);
                        return new ExchangeAmountInfo(null, null);
                    }
                    
                    BigDecimal eurAmount = eurRate.multiply(depositAmount).setScale(2, RoundingMode.HALF_UP);
                    BigDecimal usdAmount = usdRate.multiply(depositAmount).setScale(2, RoundingMode.HALF_UP);
                    
                    return new ExchangeAmountInfo(eurAmount, usdAmount);
                })
                .onErrorReturn(new ExchangeAmountInfo(null, null));
    }
}
