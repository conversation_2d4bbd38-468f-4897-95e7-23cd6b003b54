package com.skilling.payment.external;

import com.skilling.payment.external.dto.CustomerDetails;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

import java.util.Map;

@Service
public class CustomerServiceClient {

    private static final Logger logger = LoggerFactory.getLogger(CustomerServiceClient.class);

    private final WebClient webClient;

    public CustomerServiceClient(@Qualifier("customerServiceWebClient") WebClient webClient) {
        this.webClient = webClient;
    }

    public Mono<CustomerDetails> getCustomerDetails(Long customerId, Map<String, String> headers) {
        logger.info("Fetching customer details for customerId: {}", customerId);
        
        return webClient
                .get()
                .uri("/v2/customers/{customerId}", customerId)
                .headers(httpHeaders -> {
                    headers.forEach(httpHeaders::set);
                    httpHeaders.set("x-sub_caller", "payment-service");
                })
                .retrieve()
                .bodyToMono(CustomerDetails.class)
                .doOnSuccess(customer -> logger.info("Successfully fetched customer details for customerId: {}", customerId))
                .doOnError(error -> logger.error("Error fetching customer details for customerId: {}", customerId, error));
    }
}
