package com.skilling.payment.messaging;

import com.skilling.payment.messaging.dto.RealAccountCreationMessage;
import com.skilling.payment.messaging.producer.MessageProducer;
import com.skilling.payment.service.AccountMappingService;
import com.skilling.payment.service.RealAccountCreationService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.DynamicPropertyRegistry;
import org.springframework.test.context.DynamicPropertySource;
import org.testcontainers.containers.PostgreSQLContainer;
import org.testcontainers.containers.RabbitMQContainer;
import org.testcontainers.junit.jupiter.Container;
import org.testcontainers.junit.jupiter.Testcontainers;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

import static org.awaitility.Awaitility.await;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@SpringBootTest
@Testcontainers
@ActiveProfiles("test")
class MessagingIntegrationTest {

    @Container
    static PostgreSQLContainer<?> postgres = new PostgreSQLContainer<>("postgres:15-alpine")
            .withDatabaseName("payment_test")
            .withUsername("test")
            .withPassword("test");

    @Container
    static RabbitMQContainer rabbitmq = new RabbitMQContainer("rabbitmq:3.12-management-alpine")
            .withUser("test", "test");

    @DynamicPropertySource
    static void configureProperties(DynamicPropertyRegistry registry) {
        registry.add("spring.datasource.url", postgres::getJdbcUrl);
        registry.add("spring.datasource.username", postgres::getUsername);
        registry.add("spring.datasource.password", postgres::getPassword);
        
        registry.add("spring.rabbitmq.host", rabbitmq::getHost);
        registry.add("spring.rabbitmq.port", rabbitmq::getAmqpPort);
        registry.add("spring.rabbitmq.username", () -> "test");
        registry.add("spring.rabbitmq.password", () -> "test");
    }

    @Autowired
    private MessageProducer messageProducer;

    @MockBean
    private AccountMappingService accountMappingService;

    @MockBean
    private RealAccountCreationService realAccountCreationService;

    @Test
    void shouldProcessRealAccountCreationMessage() {
        // Given
        Long customerId = 12345L;
        Map<String, Object> data = Map.of(
                "customerId", customerId,
                "tradingAccountId", "TRADING123",
                "currency", "EUR",
                "accountType", "REAL",
                "license", "CYSEC",
                "customerInfo", Map.of(
                        "email", "<EMAIL>",
                        "firstName", "John",
                        "lastName", "Doe",
                        "country", "US",
                        "phone", "+**********"
                ),
                "accountDetails", Map.of(
                        "platform", "MT5",
                        "leverage", "1:100",
                        "accountGroup", "STANDARD",
                        "isDemo", false
                )
        );

        // Mock account mapping service
        when(accountMappingService.findByCustomerId(customerId)).thenReturn(Optional.empty());
        when(accountMappingService.createAccountMapping(eq(customerId), any(), eq("TRADING123")))
                .thenReturn(createMockAccountMapping());

        // When
        messageProducer.sendRealAccountCreationMessage(customerId, data);

        // Then
        await().atMost(10, TimeUnit.SECONDS).untilAsserted(() -> {
            verify(accountMappingService).findByCustomerId(customerId);
            verify(realAccountCreationService).processRealAccountCreation(any(RealAccountCreationMessage.class));
        });
    }

    @Test
    void shouldSendAndReceiveCustomMessage() {
        // Given
        Long customerId = 67890L;
        String routingKey = "test.message";
        Map<String, Object> data = Map.of("testData", "value");
        Map<String, Object> headers = Map.of("messageType", "TEST_MESSAGE");

        // When
        messageProducer.sendMessage(routingKey, customerId, data, headers);

        // Then - Message should be sent without errors
        // In a real integration test, you would have a test consumer to verify message receipt
        await().atMost(5, TimeUnit.SECONDS).untilAsserted(() -> {
            // Verify message was sent (this is a basic test)
            // In practice, you'd verify the message was received by a test consumer
        });
    }

    private com.skilling.payment.domain.AccountMapping createMockAccountMapping() {
        var mapping = new com.skilling.payment.domain.AccountMapping();
        mapping.setId(1L);
        mapping.setCustomerId(12345L);
        mapping.setPaymentAccountId("PAY123");
        mapping.setTradingAccountId("TRADING123");
        return mapping;
    }
}
