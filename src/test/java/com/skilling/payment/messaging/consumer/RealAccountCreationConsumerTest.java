package com.skilling.payment.messaging.consumer;

import com.rabbitmq.client.Channel;
import com.skilling.payment.messaging.dto.RealAccountCreationMessage;
import com.skilling.payment.service.AccountMappingService;
import com.skilling.payment.service.RealAccountCreationService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.amqp.core.Message;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.Map;
import java.util.Optional;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class RealAccountCreationConsumerTest {

    @Mock
    private RealAccountCreationService realAccountCreationService;
    
    @Mock
    private AccountMappingService accountMappingService;
    
    @Mock
    private Channel channel;
    
    @Mock
    private Message amqpMessage;

    private RealAccountCreationConsumer consumer;

    @BeforeEach
    void setUp() {
        consumer = new RealAccountCreationConsumer(realAccountCreationService, accountMappingService);
    }

    @Test
    void handleRealAccountCreation_ShouldProcessValidMessage() throws IOException {
        // Given
        RealAccountCreationMessage message = createValidMessage();
        long deliveryTag = 123L;
        
        when(accountMappingService.findByCustomerId(12345L)).thenReturn(Optional.empty());
        when(accountMappingService.createAccountMapping(eq(12345L), isNull(), eq("TRADING123")))
                .thenReturn(createMockAccountMapping());

        // When
        consumer.handleRealAccountCreation(message, deliveryTag, channel, amqpMessage);

        // Then
        verify(accountMappingService).findByCustomerId(12345L);
        verify(accountMappingService).createAccountMapping(eq(12345L), isNull(), eq("TRADING123"));
        verify(realAccountCreationService).processRealAccountCreation(message);
        verify(channel).basicAck(deliveryTag, false);
    }

    @Test
    void handleRealAccountCreation_ShouldRejectInvalidMessage() throws IOException {
        // Given
        RealAccountCreationMessage invalidMessage = createInvalidMessage();
        long deliveryTag = 123L;

        // When
        consumer.handleRealAccountCreation(invalidMessage, deliveryTag, channel, amqpMessage);

        // Then
        verify(accountMappingService, never()).findByCustomerId(any());
        verify(realAccountCreationService, never()).processRealAccountCreation(any());
        verify(channel).basicNack(deliveryTag, false, false);
    }

    @Test
    void handleRealAccountCreation_ShouldHandleExistingAccountMapping() throws IOException {
        // Given
        RealAccountCreationMessage message = createValidMessage();
        long deliveryTag = 123L;
        
        when(accountMappingService.findByCustomerId(12345L)).thenReturn(Optional.of(createMockAccountMapping()));

        // When
        consumer.handleRealAccountCreation(message, deliveryTag, channel, amqpMessage);

        // Then
        verify(accountMappingService).findByCustomerId(12345L);
        verify(accountMappingService, never()).createAccountMapping(any(), any(), any());
        verify(realAccountCreationService).processRealAccountCreation(message);
        verify(channel).basicAck(deliveryTag, false);
    }

    @Test
    void handleRealAccountCreation_ShouldHandleProcessingException() throws IOException {
        // Given
        RealAccountCreationMessage message = createValidMessage();
        long deliveryTag = 123L;
        
        when(accountMappingService.findByCustomerId(12345L)).thenReturn(Optional.empty());
        when(accountMappingService.createAccountMapping(any(), any(), any()))
                .thenThrow(new RuntimeException("Database error"));

        // When
        consumer.handleRealAccountCreation(message, deliveryTag, channel, amqpMessage);

        // Then
        verify(channel).basicNack(deliveryTag, false, true); // Requeue for retry
    }

    private RealAccountCreationMessage createValidMessage() {
        var customerInfo = new RealAccountCreationMessage.CustomerInfo(
                "<EMAIL>", "John", "Doe", "US", "+**********"
        );
        
        var accountDetails = new RealAccountCreationMessage.AccountDetails(
                "MT5", "1:100", "STANDARD", false
        );
        
        var data = new RealAccountCreationMessage.RealAccountCreationData(
                12345L, "TRADING123", "EUR", "REAL", "CYSEC", customerInfo, accountDetails
        );
        
        return new RealAccountCreationMessage(
                "REAL_ACCOUNT_CREATION", 12345L, LocalDateTime.now(), data, Map.of()
        );
    }

    private RealAccountCreationMessage createInvalidMessage() {
        return new RealAccountCreationMessage(
                "REAL_ACCOUNT_CREATION", null, LocalDateTime.now(), null, Map.of()
        );
    }

    private com.skilling.payment.domain.AccountMapping createMockAccountMapping() {
        var mapping = new com.skilling.payment.domain.AccountMapping();
        mapping.setId(1L);
        mapping.setCustomerId(12345L);
        mapping.setPaymentAccountId("PAY123");
        mapping.setTradingAccountId("TRADING123");
        return mapping;
    }
}
