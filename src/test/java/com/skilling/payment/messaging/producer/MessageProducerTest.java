package com.skilling.payment.messaging.producer;

import com.skilling.payment.config.RabbitConfig;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.amqp.core.MessagePostProcessor;
import org.springframework.amqp.rabbit.core.RabbitTemplate;

import java.util.Map;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.verify;

@ExtendWith(MockitoExtension.class)
class MessageProducerTest {

    @Mock
    private RabbitTemplate rabbitTemplate;

    private MessageProducer messageProducer;

    @BeforeEach
    void setUp() {
        messageProducer = new MessageProducer(rabbitTemplate);
    }

    @Test
    void sendRealAccountCreationMessage_ShouldSendToCorrectQueue() {
        // Given
        Long customerId = 12345L;
        Map<String, Object> data = Map.of(
                "tradingAccountId", "TRADING123",
                "currency", "EUR"
        );

        // When
        messageProducer.sendRealAccountCreationMessage(customerId, data);

        // Then
        ArgumentCaptor<String> exchangeCaptor = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> routingKeyCaptor = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Map> messageCaptor = ArgumentCaptor.forClass(Map.class);
        ArgumentCaptor<MessagePostProcessor> processorCaptor = ArgumentCaptor.forClass(MessagePostProcessor.class);

        verify(rabbitTemplate).convertAndSend(
                exchangeCaptor.capture(),
                routingKeyCaptor.capture(),
                messageCaptor.capture(),
                processorCaptor.capture()
        );

        assertThat(exchangeCaptor.getValue()).isEqualTo(RabbitConfig.EXCHANGE_NAME);
        assertThat(routingKeyCaptor.getValue()).isEqualTo(RabbitConfig.REAL_ACCOUNT_CREATION_ROUTING_KEY);
        
        Map<String, Object> sentMessage = messageCaptor.getValue();
        assertThat(sentMessage.get("type")).isEqualTo("REAL_ACCOUNT_CREATION");
        assertThat(sentMessage.get("customerId")).isEqualTo(customerId);
        assertThat(sentMessage.get("data")).isEqualTo(data);
    }

    @Test
    void sendInactiveAccountFeeMessage_ShouldSendToCorrectQueue() {
        // Given
        Long customerId = 12345L;
        Map<String, Object> data = Map.of(
                "feeAmount", "5.00",
                "currency", "EUR",
                "inactiveDays", 90
        );

        // When
        messageProducer.sendInactiveAccountFeeMessage(customerId, data);

        // Then
        ArgumentCaptor<String> exchangeCaptor = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> routingKeyCaptor = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Map> messageCaptor = ArgumentCaptor.forClass(Map.class);

        verify(rabbitTemplate).convertAndSend(
                exchangeCaptor.capture(),
                routingKeyCaptor.capture(),
                messageCaptor.capture(),
                any(MessagePostProcessor.class)
        );

        assertThat(exchangeCaptor.getValue()).isEqualTo(RabbitConfig.EXCHANGE_NAME);
        assertThat(routingKeyCaptor.getValue()).isEqualTo(RabbitConfig.INACTIVE_ACCOUNT_NOTIFICATION_ROUTING_KEY);
        
        Map<String, Object> sentMessage = messageCaptor.getValue();
        assertThat(sentMessage.get("type")).isEqualTo("INACTIVE_ACCOUNT_FEE");
        assertThat(sentMessage.get("customerId")).isEqualTo(customerId);
        assertThat(sentMessage.get("data")).isEqualTo(data);
    }

    @Test
    void sendMessage_ShouldSendWithCustomRoutingKey() {
        // Given
        String routingKey = "custom.routing.key";
        Long customerId = 12345L;
        Map<String, Object> data = Map.of("test", "data");
        Map<String, Object> headers = Map.of("messageType", "CUSTOM_MESSAGE");

        // When
        messageProducer.sendMessage(routingKey, customerId, data, headers);

        // Then
        ArgumentCaptor<String> exchangeCaptor = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> routingKeyCaptor = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Map> messageCaptor = ArgumentCaptor.forClass(Map.class);

        verify(rabbitTemplate).convertAndSend(
                exchangeCaptor.capture(),
                routingKeyCaptor.capture(),
                messageCaptor.capture(),
                any(MessagePostProcessor.class)
        );

        assertThat(exchangeCaptor.getValue()).isEqualTo(RabbitConfig.EXCHANGE_NAME);
        assertThat(routingKeyCaptor.getValue()).isEqualTo(routingKey);
        
        Map<String, Object> sentMessage = messageCaptor.getValue();
        assertThat(sentMessage.get("type")).isEqualTo("CUSTOM_MESSAGE");
        assertThat(sentMessage.get("customerId")).isEqualTo(customerId);
        assertThat(sentMessage.get("data")).isEqualTo(data);
    }

    @Test
    void sendTransactionStatusUpdate_ShouldSendCorrectMessage() {
        // Given
        Long customerId = 12345L;
        Long transactionId = 67890L;
        String status = "COMPLETED";
        Map<String, Object> additionalData = Map.of("amount", "100.00");

        // When
        messageProducer.sendTransactionStatusUpdate(customerId, transactionId, status, additionalData);

        // Then
        ArgumentCaptor<Map> messageCaptor = ArgumentCaptor.forClass(Map.class);

        verify(rabbitTemplate).convertAndSend(
                eq(RabbitConfig.EXCHANGE_NAME),
                eq("transaction.status.updated"),
                messageCaptor.capture(),
                any(MessagePostProcessor.class)
        );

        Map<String, Object> sentMessage = messageCaptor.getValue();
        assertThat(sentMessage.get("customerId")).isEqualTo(customerId);
        
        @SuppressWarnings("unchecked")
        Map<String, Object> messageData = (Map<String, Object>) sentMessage.get("data");
        assertThat(messageData.get("transactionId")).isEqualTo(transactionId);
        assertThat(messageData.get("status")).isEqualTo(status);
        assertThat(messageData.get("additionalData")).isEqualTo(additionalData);
    }
}
