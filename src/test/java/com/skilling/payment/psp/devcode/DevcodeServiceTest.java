package com.skilling.payment.psp.devcode;

import com.skilling.payment.config.PaymentProperties;
import com.skilling.payment.controller.DevcodeController.CreateTokenRequest;
import com.skilling.payment.external.CustomerServiceClient;
import com.skilling.payment.service.AccountMappingService;
import com.skilling.payment.service.TransactionService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.mock.web.MockHttpServletRequest;

import java.util.Map;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class DevcodeServiceTest {

    @Mock
    private DevcodeServiceClient devcodeClient;
    
    @Mock
    private TransactionService transactionService;
    
    @Mock
    private AccountMappingService accountMappingService;
    
    @Mock
    private CustomerServiceClient customerServiceClient;
    
    @Mock
    private PaymentProperties paymentProperties;

    private DevcodeService devcodeService;

    @BeforeEach
    void setUp() {
        devcodeService = new DevcodeService(
                devcodeClient,
                transactionService,
                accountMappingService,
                customerServiceClient,
                paymentProperties
        );
    }

    @Test
    void createPaymentToken_ShouldReturnSuccessResponse() {
        // Given
        Long customerId = 12345L;
        CreateTokenRequest request = new CreateTokenRequest(
                "100.00",
                "EUR",
                "creditcard",
                "https://example.com/return",
                "en"
        );
        MockHttpServletRequest httpRequest = new MockHttpServletRequest();
        
        // Mock payment properties
        PaymentProperties.ExternalServicesProperties externalServices = 
                new PaymentProperties.ExternalServicesProperties(
                        null, null, null, null, null,
                        new PaymentProperties.ServiceProperties("https://test-api.paymentiq.io/paymentiq/api")
                );
        when(paymentProperties.externalServices()).thenReturn(externalServices);

        // When
        Map<String, Object> response = devcodeService.createPaymentToken(
                customerId, request, "deposit", httpRequest);

        // Then
        assertThat(response).isNotNull();
        assertThat(response.get("success")).isEqualTo(true);
        assertThat(response).containsKeys("sessionId", "token", "redirectUrl");
    }

    @Test
    void createPaymentToken_ShouldHandleException() {
        // Given
        Long customerId = 12345L;
        CreateTokenRequest request = new CreateTokenRequest(
                "100.00",
                "EUR",
                "creditcard",
                "https://example.com/return",
                "en"
        );
        MockHttpServletRequest httpRequest = new MockHttpServletRequest();
        
        // Mock to throw exception
        when(paymentProperties.externalServices()).thenThrow(new RuntimeException("Test exception"));

        // When
        Map<String, Object> response = devcodeService.createPaymentToken(
                customerId, request, "deposit", httpRequest);

        // Then
        assertThat(response).isNotNull();
        assertThat(response.get("success")).isEqualTo(false);
        assertThat(response.get("error")).isEqualTo("Failed to create payment token");
    }
}
