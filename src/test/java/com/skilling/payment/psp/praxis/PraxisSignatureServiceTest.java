package com.skilling.payment.psp.praxis;

import com.skilling.payment.config.PaymentProperties;
import com.skilling.payment.psp.praxis.dto.PraxisInitRequest;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.math.BigDecimal;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

class PraxisSignatureServiceTest {

    private PraxisSignatureService signatureService;
    private PaymentProperties.PraxisLicenseProperties licenseConfig;

    @BeforeEach
    void setUp() {
        signatureService = new PraxisSignatureService();
        licenseConfig = new PaymentProperties.PraxisLicenseProperties(
                "TEST_MERCHANT",
                "TEST_SECRET",
                "TEST_APP_KEY"
        );
    }

    @Test
    void generateInitSignature_ShouldReturnValidSignature() {
        // Given
        PraxisInitRequest request = new PraxisInitRequest(
                "TEST_MERCHANT",
                "TEST_APP_KEY",
                "payment",
                "EUR",
                "12345",
                null,
                "en",
                "https://example.com/validation",
                "https://example.com/notification",
                "https://example.com/return",
                1640995200L,
                "1.3",
                "variable1",
                "127.0.0.1",
                "order123",
                new BigDecimal("100.00")
        );

        // When
        String signature = signatureService.generateInitSignature(request, licenseConfig);

        // Then
        assertThat(signature).isNotNull();
        assertThat(signature).hasSize(64); // SHA256 hex string length
        assertThat(signature).matches("[a-f0-9]+");
    }

    @Test
    void validateSignature_ShouldReturnTrueForValidSignature() {
        // Given
        List<String> fields = List.of("field1", "field2", "field3");
        String signature = signatureService.generateSignature(fields, licenseConfig.merchantSecret());

        // When
        boolean isValid = signatureService.validateSignature(fields, signature, licenseConfig);

        // Then
        assertThat(isValid).isTrue();
    }

    @Test
    void validateSignature_ShouldReturnFalseForInvalidSignature() {
        // Given
        List<String> fields = List.of("field1", "field2", "field3");
        String invalidSignature = "invalid_signature";

        // When
        boolean isValid = signatureService.validateSignature(fields, invalidSignature, licenseConfig);

        // Then
        assertThat(isValid).isFalse();
    }

    @Test
    void generateSignature_ShouldBeConsistent() {
        // Given
        List<String> fields = List.of("test", "data", "123");

        // When
        String signature1 = signatureService.generateSignature(fields, licenseConfig.merchantSecret());
        String signature2 = signatureService.generateSignature(fields, licenseConfig.merchantSecret());

        // Then
        assertThat(signature1).isEqualTo(signature2);
    }
}
